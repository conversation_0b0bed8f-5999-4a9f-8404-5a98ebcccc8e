"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filter/unified-training-filter */ \"(app-pages-browser)/./src/components/filter/unified-training-filter.tsx\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Always include completed in unified view\n    ;\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { filter, setFilter, handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setIsLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const f = {\n            ...filter\n        };\n        if (memberId && +memberId > 0) {\n            f.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        setFilter(f);\n        loadData();\n    }, []) // Only run on mount\n    ;\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.getTrainingDataStats)(filteredData);\n    }, [\n        filteredData\n    ]);\n    // Enhanced loading state management\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 252,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 251,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 271,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 270,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 287,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 289,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__.UnifiedTrainingFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 296,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                        children: \"Crew Training Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"Unified view of all training activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        unifiedData: filteredData,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                trainingSessionDues: trainingSessionDues,\n                                completedTrainingList: completedTrainingList,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 303,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 294,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"QKq5xo37/EgNwlm8c1l9ekO8sxU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy91bmlmaWVkLXRyYWluaW5nLWV4YW1wbGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFaUU7QUFDcEI7QUFJYjtBQUMrQjtBQUNDO0FBQzFCO0FBQ1M7QUFDd0M7QUFDUTtBQUNaO0FBQ047QUFDTDtBQUNyQztBQUNHO0FBQ0s7QUFRcEMsTUFBTW9CLHlCQUF5QjtRQUFDLEVBQ25DQyxXQUFXLENBQUMsRUFDWkMsV0FBVyxDQUFDLEVBQ1pDLGVBQWUsS0FBSyxFQUNNOztJQUMxQixNQUFNQyxTQUFTTCwyREFBU0E7SUFDeEIsTUFBTU0sUUFBUTtJQUNkLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHM0IsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDNEIsVUFBVUMsWUFBWSxHQUFHN0IsK0NBQVFBLENBQUM7UUFDckM4QixZQUFZO1FBQ1pDLGFBQWE7UUFDYkMsaUJBQWlCO0lBQ3JCO0lBQ0EsTUFBTSxDQUFDQyxxQkFBcUJDLHVCQUF1QixHQUFHbEMsK0NBQVFBLENBQVEsRUFBRTtJQUN4RSxNQUFNLENBQUNtQyx1QkFBdUJDLHlCQUF5QixHQUFHcEMsK0NBQVFBLENBQzlELEVBQUU7SUFFTixNQUFNLENBQUNxQyxNQUFNQyxRQUFRLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUN1QyxrQkFBa0JDLG9CQUFvQixHQUFHeEMsK0NBQVFBLENBQUMsTUFBTSwyQ0FBMkM7O0lBRTFHLE1BQU0sQ0FBQ3lDLGFBQWFDLGVBQWUsR0FBRzFDLCtDQUFRQSxDQUFNO0lBRXBELE1BQU0sRUFBRTJDLGlCQUFpQixFQUFFQyxTQUFTQyxpQkFBaUIsRUFBRSxHQUNuRHJDLDhFQUFpQkE7SUFFckIseUJBQXlCO0lBQ3pCUCxnREFBU0EsQ0FBQztRQUNOeUMsZUFBZTNCLG9FQUFjQTtJQUNqQyxHQUFHLEVBQUU7SUFFTCxxREFBcUQ7SUFDckQsTUFBTSxDQUFDK0IsMEJBQTBCLEVBQUVGLFNBQVNHLFdBQVcsRUFBRSxDQUFDLEdBQUczQyw2REFBWUEsQ0FDckVFLDhFQUEwQkEsRUFDMUI7UUFDSTBDLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNFLHVCQUF1QixDQUFDQyxLQUFLLElBQUksRUFBRTtZQUV6REMsUUFBUUMsR0FBRyxDQUNQLHlFQUNBO2dCQUNJQyxjQUFjTCxLQUFLTSxNQUFNO2dCQUN6QkMsY0FBY1AsSUFBSSxDQUFDLEVBQUU7Z0JBQ3JCUSxZQUFZUjtZQUNoQjtZQUdKakIsdUJBQXVCaUI7UUFDM0I7UUFDQVMsU0FBUyxDQUFDQztZQUNOUCxRQUFRTyxLQUFLLENBQUMsd0NBQXdDQTtRQUMxRDtJQUNKO0lBR0osd0NBQXdDO0lBQ3hDLE1BQU0sQ0FBQ0Msd0JBQXdCLEVBQUVsQixTQUFTbUIsZ0JBQWdCLEVBQUUsQ0FBQyxHQUN6RDNELDZEQUFZQSxDQUFDQyxxRUFBaUJBLEVBQUU7UUFDNUIyQyxhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTYyxvQkFBb0IsQ0FBQ1gsS0FBSyxJQUFJLEVBQUU7WUFFdERDLFFBQVFDLEdBQUcsQ0FDUCwrRUFDQTtnQkFDSUMsY0FBY0wsS0FBS00sTUFBTTtnQkFDekJDLGNBQWNQLElBQUksQ0FBQyxFQUFFO2dCQUNyQlEsWUFBWVI7WUFDaEI7WUFHSmYseUJBQXlCZTtRQUM3QjtRQUNBUyxTQUFTLENBQUNDO1lBQ05QLFFBQVFPLEtBQUssQ0FBQyxxQ0FBcUNBO1FBQ3ZEO0lBQ0o7SUFFSixzQ0FBc0M7SUFDdEMsTUFBTUksMEJBQTBCOUQsa0RBQVdBLENBQ3ZDLE9BQU8rRDtRQUNILE1BQU1DLGFBQWtCLENBQUM7UUFDekIsSUFBSTdDLFlBQVlBLFdBQVcsR0FBRztZQUMxQjZDLFdBQVdDLFFBQVEsR0FBRztnQkFBRUMsSUFBSSxDQUFDL0M7WUFBUztRQUMxQztRQUNBLElBQUlELFlBQVlBLFdBQVcsR0FBRztZQUMxQjhDLFdBQVdHLFFBQVEsR0FBRztnQkFBRUQsSUFBSSxDQUFDaEQ7WUFBUztRQUMxQztRQUNBLElBQUk2QyxPQUFPSSxRQUFRLEVBQUU7WUFDakJILFdBQVdHLFFBQVEsR0FBR0osT0FBT0ksUUFBUTtRQUN6QztRQUNBLElBQUlKLE9BQU9LLGFBQWEsRUFBRTtZQUN0QkosV0FBV0ssY0FBYyxHQUFHO2dCQUN4QkgsSUFBSUgsT0FBT0ssYUFBYSxDQUFDRSxFQUFFLENBQUNDLFFBQVE7WUFDeEM7UUFDSjtRQUNBLElBQUlSLE9BQU9TLE9BQU8sRUFBRTtZQUNoQlIsV0FBV0MsUUFBUSxHQUFHO2dCQUFFQyxJQUFJSCxPQUFPUyxPQUFPLENBQUNGLEVBQUUsQ0FBQ0MsUUFBUTtZQUFDO1FBQzNEO1FBQ0EsSUFBSVIsT0FBT1UsSUFBSSxFQUFFO1lBQ2JULFdBQVdVLE9BQU8sR0FBR1gsT0FBT1UsSUFBSTtRQUNwQyxPQUFPO1lBQ0hULFdBQVdVLE9BQU8sR0FBRztnQkFBRUMsSUFBSTtZQUFLO1FBQ3BDO1FBRUEsTUFBTWhDLHlCQUF5QjtZQUMzQmlDLFdBQVc7Z0JBQ1BiLFFBQVFDO1lBQ1o7UUFDSjtJQUNKLEdBQ0E7UUFBQzdDO1FBQVVEO1FBQVV5QjtLQUF5QjtJQUdsRCxtQ0FBbUM7SUFDbkMsTUFBTWtDLG1CQUFtQjdFLGtEQUFXQSxDQUNoQztZQUFPOEUsNkVBQW9CLEdBQUdDLGdGQUFvQixDQUFDO1FBQy9DLE1BQU1DLGtCQUF1QixDQUFDO1FBQzlCLElBQUk5RCxZQUFZQSxXQUFXLEdBQUc7WUFDMUI4RCxnQkFBZ0JiLFFBQVEsR0FBRztnQkFBRUQsSUFBSSxDQUFDaEQ7WUFBUztRQUMvQztRQUNBLElBQUk2RCxhQUFhWixRQUFRLEVBQUU7WUFDdkJhLGdCQUFnQmIsUUFBUSxHQUFHWSxhQUFhWixRQUFRO1FBQ3BEO1FBRUEsTUFBTVIsdUJBQXVCO1lBQ3pCaUIsV0FBVztnQkFDUGIsUUFBUWlCO2dCQUNSQyxRQUFRSCxZQUFZO2dCQUNwQnhELE9BQU87WUFDWDtRQUNKO0lBQ0osR0FDQTtRQUFDSjtRQUFVeUM7S0FBdUI7SUFHdEMsNkVBQTZFO0lBQzdFLE1BQU11QixjQUFjbkYsOENBQU9BLENBQUM7UUFDeEJvRCxRQUFRQyxHQUFHLENBQ1Asc0ZBQ0E7WUFDSStCLDBCQUEwQnJELENBQUFBLGdDQUFBQSwwQ0FBQUEsb0JBQXFCd0IsTUFBTSxLQUFJO1lBQ3pEOEIsNEJBQTRCcEQsQ0FBQUEsa0NBQUFBLDRDQUFBQSxzQkFBdUJzQixNQUFNLEtBQUk7WUFDN0RsQjtZQUNBaUQsU0FBUyxFQUFFdkQsZ0NBQUFBLDBDQUFBQSxtQkFBcUIsQ0FBQyxFQUFFO1lBQ25Dd0QsZUFBZSxFQUFFdEQsa0NBQUFBLDRDQUFBQSxxQkFBdUIsQ0FBQyxFQUFFO1FBQy9DO1FBR0osTUFBTXVELFNBQVM5RSw2R0FBNEJBLENBQUM7WUFDeENxQjtZQUNBRTtZQUNBUTtZQUNBSjtRQUNKO1FBRUFlLFFBQVFDLEdBQUcsQ0FDUCxtRkFDQTtZQUNJQyxjQUFja0MsT0FBT2pDLE1BQU07WUFDM0JrQyxtQkFBbUJELE9BQU9FLE1BQU0sQ0FBQyxDQUFDQyxLQUFVQztnQkFDeENELEdBQUcsQ0FBQ0MsS0FBS0MsUUFBUSxDQUFDLEdBQUcsQ0FBQ0YsR0FBRyxDQUFDQyxLQUFLQyxRQUFRLENBQUMsSUFBSSxLQUFLO2dCQUNqRCxPQUFPRjtZQUNYLEdBQUcsQ0FBQztZQUNKbkMsY0FBY2dDLE1BQU0sQ0FBQyxFQUFFO1lBQ3ZCL0IsWUFBWStCO1FBQ2hCO1FBR0osT0FBT0E7SUFDWCxHQUFHO1FBQ0N6RDtRQUNBRTtRQUNBUTtRQUNBSjtLQUNIO0lBRUQsK0RBQStEO0lBQy9ELE1BQU0sRUFBRTJCLE1BQU0sRUFBRThCLFNBQVMsRUFBRUMsa0JBQWtCLEVBQUVDLFlBQVksRUFBRSxHQUN6RHBGLDJGQUF5QkEsQ0FBQztRQUN0QnFGLGVBQWUsQ0FBQztRQUNoQmQ7SUFDSjtJQUVKLG1FQUFtRTtJQUNuRSxNQUFNZSxXQUFXakcsa0RBQVdBLENBQUM7UUFDekJ3QixhQUFhO1FBQ2IsTUFBTXNDLHdCQUF3QkM7UUFDOUIsTUFBTWMsaUJBQWlCM0MsTUFBTTZCO1FBQzdCdkMsYUFBYTtJQUNqQixHQUFHO1FBQUN1QztRQUFRN0I7UUFBTTRCO1FBQXlCZTtLQUFpQjtJQUU1RCwrQkFBK0I7SUFDL0IvRSxnREFBU0EsQ0FBQztRQUNOLE1BQU1vRyxJQUF1QjtZQUFFLEdBQUduQyxNQUFNO1FBQUM7UUFDekMsSUFBSTVDLFlBQVksQ0FBQ0EsV0FBVyxHQUFHO1lBQzNCK0UsRUFBRTFCLE9BQU8sR0FBRztnQkFBRUYsSUFBSTtvQkFBRUMsVUFBVSxDQUFDcEQ7Z0JBQVM7WUFBRTtRQUM5QztRQUNBMEUsVUFBVUs7UUFDVkQ7SUFDSixHQUFHLEVBQUUsRUFBRSxvQkFBb0I7O0lBRTNCLE1BQU1FLFFBQVFwRyw4Q0FBT0EsQ0FBQztRQUNsQixPQUFPUyxxR0FBb0JBLENBQUN1RjtJQUNoQyxHQUFHO1FBQUNBO0tBQWE7SUFFakIsb0NBQW9DO0lBQ3BDLE1BQU1LLHlCQUNGdEUsdUJBQXVCQSxvQkFBb0J3QixNQUFNLEdBQUc7SUFDeEQsTUFBTStDLG1CQUNGckUseUJBQXlCQSxzQkFBc0JzQixNQUFNLEdBQUc7SUFFNUQsaUNBQWlDO0lBQ2pDLE1BQU1nRCxtQkFBbUI7UUFDckIsTUFBTUMsV0FBVyxFQUFFO1FBQ25CLElBQUkzRCxhQUFhMkQsU0FBU0MsSUFBSSxDQUFDO1FBQy9CLElBQUk1QyxrQkFBa0IyQyxTQUFTQyxJQUFJLENBQUM7UUFDcEMsSUFBSWpGLFdBQVdnRixTQUFTQyxJQUFJLENBQUM7UUFDN0IsT0FBT0Q7SUFDWDtJQUVBLGtDQUFrQztJQUNsQyxNQUFNRSxtQkFBbUI7WUFBQyxFQUFFQyxNQUFNLEVBQXdCOzZCQUN0RCw4REFBQ0M7WUFBSUMsV0FBVTtzQkFDWCw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNYLDhEQUFDN0Ysb0ZBQU9BO3dCQUFDNkYsV0FBVTs7Ozs7O2tDQUNuQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNYLDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBc0I7Ozs7Ozs0QkFHbENGLE9BQU9wRCxNQUFNLEdBQUcsbUJBQ2IsOERBQUN1RDtnQ0FBRUQsV0FBVTs7b0NBQWdDO29DQUNyQkYsT0FBT0ksSUFBSSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUXhELDREQUE0RDtJQUM1RCxNQUFNQywwQkFBMEI7WUFBQyxFQUFFTCxNQUFNLEVBQXdCOzZCQUM3RCw4REFBQ0M7WUFBSUMsV0FBVTtzQkFDWCw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNYLDhEQUFDN0Ysb0ZBQU9BO3dCQUFDNkYsV0FBVTs7Ozs7O2tDQUNuQiw4REFBQ0k7OzRCQUFLOzRCQUFTTixPQUFPSSxJQUFJLENBQUM7NEJBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLN0Msb0JBQW9CO0lBQ3BCLElBQ0ksQ0FBQ3hFLGVBQ0EsQ0FBQ3pCLHVFQUFhQSxDQUFDLGlCQUFpQnlCLGdCQUM3QixDQUFDekIsdUVBQWFBLENBQUMsaUJBQWlCeUIsZ0JBQ2hDLENBQUN6Qix1RUFBYUEsQ0FBQyxtQkFBbUJ5QixnQkFDbEMsQ0FBQ3pCLHVFQUFhQSxDQUFDLHdCQUF3QnlCLGNBQzdDO1FBQ0UsT0FBTyxDQUFDQSw0QkFDSiw4REFBQ3hCLHFEQUFPQTs7OztzQ0FFUiw4REFBQ0EscURBQU9BO1lBQUNtRyxjQUFhOzs7Ozs7SUFFOUI7SUFFQSxxQkFDSSw4REFBQ047UUFBSUMsV0FBVTs7MEJBRVgsOERBQUN0RyxnREFBSUE7MEJBQ0QsNEVBQUNJLDZGQUFxQkE7b0JBQ2xCUyxVQUFVQTtvQkFDVitGLFVBQVVwQjs7Ozs7Ozs7Ozs7MEJBSWxCLDhEQUFDeEYsZ0RBQUlBO2dCQUFDc0csV0FBVTswQkFDWiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUVYLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ0Q7O2tEQUNHLDhEQUFDcEcseURBQUVBO2tEQUFDOzs7Ozs7a0RBQ0osOERBQUNzRzt3Q0FBRUQsV0FBVTtrREFBcUM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0xRCw4REFBQ0Q7c0NBQ0lwRixhQUNELENBQUM2RSwwQkFDRCxDQUFDQyxtQkFDRywrQ0FBK0M7MENBQy9DLDhEQUFDSTtnQ0FBaUJDLFFBQVFKOzs7Ozs0Q0FDMUIvRSxhQUNENkUsQ0FBQUEsMEJBQTBCQyxnQkFBZSxJQUN4QyxvREFBb0Q7MENBQ3BELDhEQUFDTTtnQ0FBSUMsV0FBVTs7a0RBQ1gsOERBQUN4Ryx5RUFBb0JBO3dDQUNqQjhFLGFBQWFhO3dDQUNidkQsbUJBQW1CQTt3Q0FDbkJKLGtCQUFrQkE7d0NBQ2xCakIsVUFBVUE7d0NBQ1ZDLGNBQWNBO3dDQUNkK0YsYUFBYTt3Q0FDYkMsVUFBVTs7Ozs7O2tEQUVkLDhEQUFDTDt3Q0FDR0wsUUFBUUo7Ozs7Ozs7Ozs7OzRDQUloQix5QkFBeUI7MENBQ3pCLDhEQUFDbEcseUVBQW9CQTtnQ0FDakIwQixxQkFBcUJBO2dDQUNyQkUsdUJBQXVCQTtnQ0FDdkJRLG1CQUFtQkE7Z0NBQ25CSixrQkFBa0JBO2dDQUNsQmpCLFVBQVVBO2dDQUNWQyxjQUFjQTtnQ0FDZCtGLGFBQWE7Z0NBQ2JDLFVBQVU7Ozs7Ozs7Ozs7O3dCQU1yQjdGLGFBQ0k2RSxDQUFBQSwwQkFBMEJDLGdCQUFlLG1CQUN0Qyw4REFBQ007NEJBQUlDLFdBQVU7c0NBQ1gsNEVBQUNDOzBDQUFFOzs7Ozs7Ozs7Ozt3QkFTZCxDQUFDdEYsYUFBYTJELFlBQVk1QixNQUFNLEtBQUssbUJBQ2xDLDhEQUFDcUQ7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDQztvQ0FBRUQsV0FBVTs4Q0FBVTs7Ozs7OzhDQUd2Qiw4REFBQ0M7b0NBQUVELFdBQVU7OENBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3hELEVBQUM7R0FoV1kzRjs7UUFLTUQsdURBQVNBO1FBa0JwQlgsMEVBQWlCQTtRQVF3Q0oseURBQVlBO1FBMEJyRUEseURBQVlBO1FBMEhaVSx1RkFBeUJBOzs7S0FuTHBCTTtBQWtXYiwrREFBZUEsc0JBQXNCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy91bmlmaWVkLXRyYWluaW5nLWV4YW1wbGUudHN4P2EyN2MiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VMYXp5UXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCdcbmltcG9ydCB7XG4gICAgVFJBSU5JTkdfU0VTU0lPTlMsXG4gICAgUkVBRF9UUkFJTklOR19TRVNTSU9OX0RVRVMsXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xuaW1wb3J0IHsgVW5pZmllZFRyYWluaW5nVGFibGUgfSBmcm9tICcuL3VuaWZpZWQtdHJhaW5pbmctdGFibGUnXG5pbXBvcnQgeyB1c2VWZXNzZWxJY29uRGF0YSB9IGZyb20gJ0AvYXBwL2xpYi92ZXNzZWwtaWNvbi1oZWxwZXInXG5pbXBvcnQgeyBDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpJ1xuaW1wb3J0IHsgSDIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdHlwb2dyYXBoeSdcbmltcG9ydCB7IGdldFRyYWluaW5nRGF0YVN0YXRzIH0gZnJvbSAnQC9hcHAvdWkvY3Jldy10cmFpbmluZy91dGlscy9jcmV3LXRyYWluaW5nLXV0aWxzJ1xuaW1wb3J0IHsgbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YSB9IGZyb20gJ0AvYXBwL3VpL2NyZXctdHJhaW5pbmcvdXRpbHMvY3Jldy10cmFpbmluZy11dGlscydcbmltcG9ydCB7IFVuaWZpZWRUcmFpbmluZ0ZpbHRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9maWx0ZXIvdW5pZmllZC10cmFpbmluZy1maWx0ZXInXG5pbXBvcnQgeyB1c2VVbmlmaWVkVHJhaW5pbmdGaWx0ZXJzIH0gZnJvbSAnLi9ob29rcy91c2VVbmlmaWVkVHJhaW5pbmdGaWx0ZXJzJ1xuaW1wb3J0IHsgZ2V0UGVybWlzc2lvbnMsIGhhc1Blcm1pc3Npb24gfSBmcm9tICdAL2FwcC9oZWxwZXJzL3VzZXJIZWxwZXInXG5pbXBvcnQgTG9hZGluZyBmcm9tICdAL2FwcC9sb2FkaW5nJ1xuaW1wb3J0IHsgTG9hZGVyMiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcblxuaW50ZXJmYWNlIFVuaWZpZWRUcmFpbmluZ0V4YW1wbGVQcm9wcyB7XG4gICAgdmVzc2VsSWQ/OiBudW1iZXJcbiAgICBtZW1iZXJJZD86IG51bWJlclxuICAgIGlzVmVzc2VsVmlldz86IGJvb2xlYW5cbn1cblxuZXhwb3J0IGNvbnN0IFVuaWZpZWRUcmFpbmluZ0V4YW1wbGUgPSAoe1xuICAgIHZlc3NlbElkID0gMCxcbiAgICBtZW1iZXJJZCA9IDAsXG4gICAgaXNWZXNzZWxWaWV3ID0gZmFsc2UsXG59OiBVbmlmaWVkVHJhaW5pbmdFeGFtcGxlUHJvcHMpID0+IHtcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICAgIGNvbnN0IGxpbWl0ID0gMTAwXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gICAgY29uc3QgW3BhZ2VJbmZvLCBzZXRQYWdlSW5mb10gPSB1c2VTdGF0ZSh7XG4gICAgICAgIHRvdGFsQ291bnQ6IDAsXG4gICAgICAgIGhhc05leHRQYWdlOiBmYWxzZSxcbiAgICAgICAgaGFzUHJldmlvdXNQYWdlOiBmYWxzZSxcbiAgICB9KVxuICAgIGNvbnN0IFt0cmFpbmluZ1Nlc3Npb25EdWVzLCBzZXRUcmFpbmluZ1Nlc3Npb25EdWVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgICBjb25zdCBbY29tcGxldGVkVHJhaW5pbmdMaXN0LCBzZXRDb21wbGV0ZWRUcmFpbmluZ0xpc3RdID0gdXNlU3RhdGU8YW55W10+KFxuICAgICAgICBbXSxcbiAgICApXG4gICAgY29uc3QgW3BhZ2UsIHNldFBhZ2VdID0gdXNlU3RhdGUoMClcbiAgICBjb25zdCBbaW5jbHVkZUNvbXBsZXRlZCwgc2V0SW5jbHVkZUNvbXBsZXRlZF0gPSB1c2VTdGF0ZSh0cnVlKSAvLyBBbHdheXMgaW5jbHVkZSBjb21wbGV0ZWQgaW4gdW5pZmllZCB2aWV3XG5cbiAgICBjb25zdCBbcGVybWlzc2lvbnMsIHNldFBlcm1pc3Npb25zXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXG5cbiAgICBjb25zdCB7IGdldFZlc3NlbFdpdGhJY29uLCBsb2FkaW5nOiB2ZXNzZWxEYXRhTG9hZGluZyB9ID1cbiAgICAgICAgdXNlVmVzc2VsSWNvbkRhdGEoKVxuXG4gICAgLy8gSW5pdGlhbGl6ZSBwZXJtaXNzaW9uc1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHNldFBlcm1pc3Npb25zKGdldFBlcm1pc3Npb25zKVxuICAgIH0sIFtdKVxuXG4gICAgLy8gUXVlcnkgZm9yIHRyYWluaW5nIHNlc3Npb24gZHVlcyAob3ZlcmR1ZS91cGNvbWluZylcbiAgICBjb25zdCBbcXVlcnlUcmFpbmluZ1Nlc3Npb25EdWVzLCB7IGxvYWRpbmc6IGR1ZXNMb2FkaW5nIH1dID0gdXNlTGF6eVF1ZXJ5KFxuICAgICAgICBSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUyxcbiAgICAgICAge1xuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMubm9kZXMgfHwgW11cblxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgICAgICAgICAn8J+UjSBbVW5pZmllZFRyYWluaW5nRXhhbXBsZV0gUmF3IHRyYWluaW5nIHNlc3Npb24gZHVlcyBkYXRhOicsXG4gICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvdGFsUmVjb3JkczogZGF0YS5sZW5ndGgsXG4gICAgICAgICAgICAgICAgICAgICAgICBzYW1wbGVSZWNvcmQ6IGRhdGFbMF0sXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGxSZWNvcmRzOiBkYXRhLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIClcblxuICAgICAgICAgICAgICAgIHNldFRyYWluaW5nU2Vzc2lvbkR1ZXMoZGF0YSlcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgdHJhaW5pbmcgc2Vzc2lvbiBkdWVzOicsIGVycm9yKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICApXG5cbiAgICAvLyBRdWVyeSBmb3IgY29tcGxldGVkIHRyYWluaW5nIHNlc3Npb25zXG4gICAgY29uc3QgW3F1ZXJ5Q29tcGxldGVkVHJhaW5pbmcsIHsgbG9hZGluZzogY29tcGxldGVkTG9hZGluZyB9XSA9XG4gICAgICAgIHVzZUxhenlRdWVyeShUUkFJTklOR19TRVNTSU9OUywge1xuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbnMubm9kZXMgfHwgW11cblxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgICAgICAgICAn8J+UjSBbVW5pZmllZFRyYWluaW5nRXhhbXBsZV0gUmF3IGNvbXBsZXRlZCB0cmFpbmluZyBzZXNzaW9ucyBkYXRhOicsXG4gICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvdGFsUmVjb3JkczogZGF0YS5sZW5ndGgsXG4gICAgICAgICAgICAgICAgICAgICAgICBzYW1wbGVSZWNvcmQ6IGRhdGFbMF0sXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGxSZWNvcmRzOiBkYXRhLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIClcblxuICAgICAgICAgICAgICAgIHNldENvbXBsZXRlZFRyYWluaW5nTGlzdChkYXRhKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjb21wbGV0ZWQgdHJhaW5pbmc6JywgZXJyb3IpXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KVxuXG4gICAgLy8gTG9hZCB0cmFpbmluZyBzZXNzaW9uIGR1ZXMgZnVuY3Rpb25cbiAgICBjb25zdCBsb2FkVHJhaW5pbmdTZXNzaW9uRHVlcyA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAoZmlsdGVyOiBhbnkpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGR1ZXNGaWx0ZXI6IGFueSA9IHt9XG4gICAgICAgICAgICBpZiAobWVtYmVySWQgJiYgbWVtYmVySWQgPiAwKSB7XG4gICAgICAgICAgICAgICAgZHVlc0ZpbHRlci5tZW1iZXJJRCA9IHsgZXE6ICttZW1iZXJJZCB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAodmVzc2VsSWQgJiYgdmVzc2VsSWQgPiAwKSB7XG4gICAgICAgICAgICAgICAgZHVlc0ZpbHRlci52ZXNzZWxJRCA9IHsgZXE6ICt2ZXNzZWxJZCB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZmlsdGVyLnZlc3NlbElEKSB7XG4gICAgICAgICAgICAgICAgZHVlc0ZpbHRlci52ZXNzZWxJRCA9IGZpbHRlci52ZXNzZWxJRFxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGZpbHRlci50cmFpbmluZ1R5cGVzKSB7XG4gICAgICAgICAgICAgICAgZHVlc0ZpbHRlci50cmFpbmluZ1R5cGVJRCA9IHtcbiAgICAgICAgICAgICAgICAgICAgZXE6IGZpbHRlci50cmFpbmluZ1R5cGVzLmlkLmNvbnRhaW5zLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChmaWx0ZXIubWVtYmVycykge1xuICAgICAgICAgICAgICAgIGR1ZXNGaWx0ZXIubWVtYmVySUQgPSB7IGVxOiBmaWx0ZXIubWVtYmVycy5pZC5jb250YWlucyB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZmlsdGVyLmRhdGUpIHtcbiAgICAgICAgICAgICAgICBkdWVzRmlsdGVyLmR1ZURhdGUgPSBmaWx0ZXIuZGF0ZVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBkdWVzRmlsdGVyLmR1ZURhdGUgPSB7IG5lOiBudWxsIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgYXdhaXQgcXVlcnlUcmFpbmluZ1Nlc3Npb25EdWVzKHtcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiBkdWVzRmlsdGVyLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9KVxuICAgICAgICB9LFxuICAgICAgICBbbWVtYmVySWQsIHZlc3NlbElkLCBxdWVyeVRyYWluaW5nU2Vzc2lvbkR1ZXNdLFxuICAgIClcblxuICAgIC8vIExvYWQgY29tcGxldGVkIHRyYWluaW5nIGZ1bmN0aW9uXG4gICAgY29uc3QgbG9hZFRyYWluaW5nTGlzdCA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAoc3RhcnRQYWdlOiBudW1iZXIgPSAwLCBzZWFyY2hGaWx0ZXI6IGFueSA9IHt9KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBjb21wbGV0ZWRGaWx0ZXI6IGFueSA9IHt9XG4gICAgICAgICAgICBpZiAodmVzc2VsSWQgJiYgdmVzc2VsSWQgPiAwKSB7XG4gICAgICAgICAgICAgICAgY29tcGxldGVkRmlsdGVyLnZlc3NlbElEID0geyBlcTogK3Zlc3NlbElkIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChzZWFyY2hGaWx0ZXIudmVzc2VsSUQpIHtcbiAgICAgICAgICAgICAgICBjb21wbGV0ZWRGaWx0ZXIudmVzc2VsSUQgPSBzZWFyY2hGaWx0ZXIudmVzc2VsSURcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgYXdhaXQgcXVlcnlDb21wbGV0ZWRUcmFpbmluZyh7XG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XG4gICAgICAgICAgICAgICAgICAgIGZpbHRlcjogY29tcGxldGVkRmlsdGVyLFxuICAgICAgICAgICAgICAgICAgICBvZmZzZXQ6IHN0YXJ0UGFnZSAqIDIwLCAvLyBVc2UgMjAgaXRlbXMgcGVyIHBhZ2UgdG8gbWF0Y2ggcGFnZVNpemVcbiAgICAgICAgICAgICAgICAgICAgbGltaXQ6IDIwLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9KVxuICAgICAgICB9LFxuICAgICAgICBbdmVzc2VsSWQsIHF1ZXJ5Q29tcGxldGVkVHJhaW5pbmddLFxuICAgIClcblxuICAgIC8vIE1lbW9pemUgdGhlIHVuaWZpZWQgZGF0YSBjYWxjdWxhdGlvbiB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlY2FsY3VsYXRpb25zXG4gICAgY29uc3QgdW5pZmllZERhdGEgPSB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAn8J+UjSBbVW5pZmllZFRyYWluaW5nRXhhbXBsZV0gSW5wdXQgZGF0YSBmb3IgbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YTonLFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXNDb3VudDogdHJhaW5pbmdTZXNzaW9uRHVlcz8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgICAgICAgY29tcGxldGVkVHJhaW5pbmdMaXN0Q291bnQ6IGNvbXBsZXRlZFRyYWluaW5nTGlzdD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgICAgICAgaW5jbHVkZUNvbXBsZXRlZCxcbiAgICAgICAgICAgICAgICBzYW1wbGVEdWU6IHRyYWluaW5nU2Vzc2lvbkR1ZXM/LlswXSxcbiAgICAgICAgICAgICAgICBzYW1wbGVDb21wbGV0ZWQ6IGNvbXBsZXRlZFRyYWluaW5nTGlzdD8uWzBdLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgKVxuXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IG1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGEoe1xuICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlcyxcbiAgICAgICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCxcbiAgICAgICAgICAgIGdldFZlc3NlbFdpdGhJY29uLFxuICAgICAgICAgICAgaW5jbHVkZUNvbXBsZXRlZCxcbiAgICAgICAgfSlcblxuICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICAgICfwn5SNIFtVbmlmaWVkVHJhaW5pbmdFeGFtcGxlXSBSZXN1bHQgZnJvbSBtZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhOicsXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgdG90YWxSZWNvcmRzOiByZXN1bHQubGVuZ3RoLFxuICAgICAgICAgICAgICAgIGNhdGVnb3J5QnJlYWtkb3duOiByZXN1bHQucmVkdWNlKChhY2M6IGFueSwgaXRlbTogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGFjY1tpdGVtLmNhdGVnb3J5XSA9IChhY2NbaXRlbS5jYXRlZ29yeV0gfHwgMCkgKyAxXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBhY2NcbiAgICAgICAgICAgICAgICB9LCB7fSksXG4gICAgICAgICAgICAgICAgc2FtcGxlUmVjb3JkOiByZXN1bHRbMF0sXG4gICAgICAgICAgICAgICAgYWxsUmVjb3JkczogcmVzdWx0LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgKVxuXG4gICAgICAgIHJldHVybiByZXN1bHRcbiAgICB9LCBbXG4gICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXMsXG4gICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCxcbiAgICAgICAgZ2V0VmVzc2VsV2l0aEljb24sXG4gICAgICAgIGluY2x1ZGVDb21wbGV0ZWQsXG4gICAgXSlcblxuICAgIC8vIFVzZSB1bmlmaWVkIHRyYWluaW5nIGZpbHRlcnMgaG9vayB3aXRoIGNsaWVudC1zaWRlIGZpbHRlcmluZ1xuICAgIGNvbnN0IHsgZmlsdGVyLCBzZXRGaWx0ZXIsIGhhbmRsZUZpbHRlckNoYW5nZSwgZmlsdGVyZWREYXRhIH0gPVxuICAgICAgICB1c2VVbmlmaWVkVHJhaW5pbmdGaWx0ZXJzKHtcbiAgICAgICAgICAgIGluaXRpYWxGaWx0ZXI6IHt9LFxuICAgICAgICAgICAgdW5pZmllZERhdGEsXG4gICAgICAgIH0pXG5cbiAgICAvLyBNZW1vaXplIHRoZSBsb2FkIGRhdGEgZnVuY3Rpb24gdG8gcHJldmVudCB1bm5lY2Vzc2FyeSByZS1yZW5kZXJzXG4gICAgY29uc3QgbG9hZERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgICAgICBhd2FpdCBsb2FkVHJhaW5pbmdTZXNzaW9uRHVlcyhmaWx0ZXIpXG4gICAgICAgIGF3YWl0IGxvYWRUcmFpbmluZ0xpc3QocGFnZSwgZmlsdGVyKVxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfSwgW2ZpbHRlciwgcGFnZSwgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMsIGxvYWRUcmFpbmluZ0xpc3RdKVxuXG4gICAgLy8gTG9hZCBkYXRhIG9uIGNvbXBvbmVudCBtb3VudFxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IGY6IHsgbWVtYmVycz86IGFueSB9ID0geyAuLi5maWx0ZXIgfVxuICAgICAgICBpZiAobWVtYmVySWQgJiYgK21lbWJlcklkID4gMCkge1xuICAgICAgICAgICAgZi5tZW1iZXJzID0geyBpZDogeyBjb250YWluczogK21lbWJlcklkIH0gfVxuICAgICAgICB9XG4gICAgICAgIHNldEZpbHRlcihmKVxuICAgICAgICBsb2FkRGF0YSgpXG4gICAgfSwgW10pIC8vIE9ubHkgcnVuIG9uIG1vdW50XG5cbiAgICBjb25zdCBzdGF0cyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgICAgICByZXR1cm4gZ2V0VHJhaW5pbmdEYXRhU3RhdHMoZmlsdGVyZWREYXRhKVxuICAgIH0sIFtmaWx0ZXJlZERhdGFdKVxuXG4gICAgLy8gRW5oYW5jZWQgbG9hZGluZyBzdGF0ZSBtYW5hZ2VtZW50XG4gICAgY29uc3QgaGFzT3ZlcmR1ZVVwY29taW5nRGF0YSA9XG4gICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXMgJiYgdHJhaW5pbmdTZXNzaW9uRHVlcy5sZW5ndGggPiAwXG4gICAgY29uc3QgaGFzQ29tcGxldGVkRGF0YSA9XG4gICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCAmJiBjb21wbGV0ZWRUcmFpbmluZ0xpc3QubGVuZ3RoID4gMFxuXG4gICAgLy8gQ3JlYXRlIGRldGFpbGVkIGxvYWRpbmcgc3RhdHVzXG4gICAgY29uc3QgZ2V0TG9hZGluZ1N0YXR1cyA9ICgpID0+IHtcbiAgICAgICAgY29uc3Qgc3RhdHVzZXMgPSBbXVxuICAgICAgICBpZiAoZHVlc0xvYWRpbmcpIHN0YXR1c2VzLnB1c2goJ292ZXJkdWUvdXBjb21pbmcgdHJhaW5pbmcnKVxuICAgICAgICBpZiAoY29tcGxldGVkTG9hZGluZykgc3RhdHVzZXMucHVzaCgnY29tcGxldGVkIHRyYWluaW5nJylcbiAgICAgICAgaWYgKGlzTG9hZGluZykgc3RhdHVzZXMucHVzaCgndHJhaW5pbmcgZGF0YScpXG4gICAgICAgIHJldHVybiBzdGF0dXNlc1xuICAgIH1cblxuICAgIC8vIENvbXByZWhlbnNpdmUgbG9hZGluZyBjb21wb25lbnRcbiAgICBjb25zdCBMb2FkaW5nSW5kaWNhdG9yID0gKHsgc3RhdHVzIH06IHsgc3RhdHVzOiBzdHJpbmdbXSB9KSA9PiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpbiBteC1hdXRvIHRleHQtcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgTG9hZGluZyB0cmFpbmluZyBkYXRhLi4uXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAge3N0YXR1cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3VycmVudGx5IGxvYWRpbmc6IHtzdGF0dXMuam9pbignLCAnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICApXG5cbiAgICAvLyBQYXJ0aWFsIGxvYWRpbmcgY29tcG9uZW50IGZvciB3aGVuIHNvbWUgZGF0YSBpcyBhdmFpbGFibGVcbiAgICBjb25zdCBQYXJ0aWFsTG9hZGluZ0luZGljYXRvciA9ICh7IHN0YXR1cyB9OiB7IHN0YXR1czogc3RyaW5nW10gfSkgPT4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTQgYmctbXV0ZWQvMzAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj5Mb2FkaW5nIHtzdGF0dXMuam9pbignLCAnKX0uLi48L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgKVxuXG4gICAgLy8gQ2hlY2sgcGVybWlzc2lvbnNcbiAgICBpZiAoXG4gICAgICAgICFwZXJtaXNzaW9ucyB8fFxuICAgICAgICAoIWhhc1Blcm1pc3Npb24oJ0VESVRfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykgJiZcbiAgICAgICAgICAgICFoYXNQZXJtaXNzaW9uKCdWSUVXX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpICYmXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignUkVDT1JEX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpICYmXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignVklFV19NRU1CRVJfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykpXG4gICAgKSB7XG4gICAgICAgIHJldHVybiAhcGVybWlzc2lvbnMgPyAoXG4gICAgICAgICAgICA8TG9hZGluZyAvPlxuICAgICAgICApIDogKFxuICAgICAgICAgICAgPExvYWRpbmcgZXJyb3JNZXNzYWdlPVwiT29wcyBZb3UgZG8gbm90IGhhdmUgdGhlIHBlcm1pc3Npb24gdG8gdmlldyB0aGlzIHNlY3Rpb24uXCIgLz5cbiAgICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgey8qIEZpbHRlciBTZWN0aW9uICovfVxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPFVuaWZpZWRUcmFpbmluZ0ZpbHRlclxuICAgICAgICAgICAgICAgICAgICBtZW1iZXJJZD17bWVtYmVySWR9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWx0ZXJDaGFuZ2V9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgey8qIEhlYWRlciB3aXRoIHN0YXRpc3RpY3MgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBzbTppdGVtcy1jZW50ZXIgc206anVzdGlmeS1iZXR3ZWVuIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIMj5DcmV3IFRyYWluaW5nIE92ZXJ2aWV3PC9IMj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVuaWZpZWQgdmlldyBvZiBhbGwgdHJhaW5pbmcgYWN0aXZpdGllc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgey8qIFVuaWZpZWQgdHJhaW5pbmcgdGFibGUgd2l0aCBlbmhhbmNlZCBsb2FkaW5nIHN0YXRlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICFoYXNPdmVyZHVlVXBjb21pbmdEYXRhICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAhaGFzQ29tcGxldGVkRGF0YSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBGdWxsIGxvYWRpbmcgc3RhdGUgd2hlbiBubyBkYXRhIGlzIGF2YWlsYWJsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2FkaW5nSW5kaWNhdG9yIHN0YXR1cz17Z2V0TG9hZGluZ1N0YXR1cygpfSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IGlzTG9hZGluZyAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAoaGFzT3ZlcmR1ZVVwY29taW5nRGF0YSB8fCBoYXNDb21wbGV0ZWREYXRhKSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBQYXJ0aWFsIGxvYWRpbmcgc3RhdGUgd2hlbiBzb21lIGRhdGEgaXMgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFVuaWZpZWRUcmFpbmluZ1RhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1bmlmaWVkRGF0YT17ZmlsdGVyZWREYXRhfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0VmVzc2VsV2l0aEljb249e2dldFZlc3NlbFdpdGhJY29ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5jbHVkZUNvbXBsZXRlZD17aW5jbHVkZUNvbXBsZXRlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzVmVzc2VsVmlldz17aXNWZXNzZWxWaWV3fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hvd1Rvb2xiYXI9e2ZhbHNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9ezIwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGFydGlhbExvYWRpbmdJbmRpY2F0b3JcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1cz17Z2V0TG9hZGluZ1N0YXR1cygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBOb3JtYWwgc3RhdGUgd2l0aCBkYXRhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFVuaWZpZWRUcmFpbmluZ1RhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXM9e3RyYWluaW5nU2Vzc2lvbkR1ZXN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdD17Y29tcGxldGVkVHJhaW5pbmdMaXN0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRWZXNzZWxXaXRoSWNvbj17Z2V0VmVzc2VsV2l0aEljb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2x1ZGVDb21wbGV0ZWQ9e2luY2x1ZGVDb21wbGV0ZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNWZXNzZWxWaWV3PXtpc1Zlc3NlbFZpZXd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9ezIwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogTG9hZGluZyBzdGF0dXMgaW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIChoYXNPdmVyZHVlVXBjb21pbmdEYXRhIHx8IGhhc0NvbXBsZXRlZERhdGEpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU29tZSBkYXRhIGlzIHN0aWxsIGxvYWRpbmcuIFRoZSB0YWJsZSB3aWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGUgYXV0b21hdGljYWxseSB3aGVuIG5ldyBkYXRhIGJlY29tZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF2YWlsYWJsZS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogTm8gZGF0YSBzdGF0ZSAqL31cbiAgICAgICAgICAgICAgICAgICAgeyFpc0xvYWRpbmcgJiYgdW5pZmllZERhdGEubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vIHRyYWluaW5nIGRhdGEgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUcnkgYWRqdXN0aW5nIHlvdXIgZmlsdGVycyBvciByZWZyZXNoIHRoZSBkYXRhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuICAgIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgVW5pZmllZFRyYWluaW5nRXhhbXBsZVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZUNhbGxiYWNrIiwidXNlTGF6eVF1ZXJ5IiwiVFJBSU5JTkdfU0VTU0lPTlMiLCJSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUyIsIlVuaWZpZWRUcmFpbmluZ1RhYmxlIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJDYXJkIiwiSDIiLCJnZXRUcmFpbmluZ0RhdGFTdGF0cyIsIm1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGEiLCJVbmlmaWVkVHJhaW5pbmdGaWx0ZXIiLCJ1c2VVbmlmaWVkVHJhaW5pbmdGaWx0ZXJzIiwiZ2V0UGVybWlzc2lvbnMiLCJoYXNQZXJtaXNzaW9uIiwiTG9hZGluZyIsIkxvYWRlcjIiLCJ1c2VSb3V0ZXIiLCJVbmlmaWVkVHJhaW5pbmdFeGFtcGxlIiwidmVzc2VsSWQiLCJtZW1iZXJJZCIsImlzVmVzc2VsVmlldyIsInJvdXRlciIsImxpbWl0IiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwicGFnZUluZm8iLCJzZXRQYWdlSW5mbyIsInRvdGFsQ291bnQiLCJoYXNOZXh0UGFnZSIsImhhc1ByZXZpb3VzUGFnZSIsInRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJzZXRUcmFpbmluZ1Nlc3Npb25EdWVzIiwiY29tcGxldGVkVHJhaW5pbmdMaXN0Iiwic2V0Q29tcGxldGVkVHJhaW5pbmdMaXN0IiwicGFnZSIsInNldFBhZ2UiLCJpbmNsdWRlQ29tcGxldGVkIiwic2V0SW5jbHVkZUNvbXBsZXRlZCIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJnZXRWZXNzZWxXaXRoSWNvbiIsImxvYWRpbmciLCJ2ZXNzZWxEYXRhTG9hZGluZyIsInF1ZXJ5VHJhaW5pbmdTZXNzaW9uRHVlcyIsImR1ZXNMb2FkaW5nIiwiZmV0Y2hQb2xpY3kiLCJvbkNvbXBsZXRlZCIsInJlc3BvbnNlIiwiZGF0YSIsInJlYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwibm9kZXMiLCJjb25zb2xlIiwibG9nIiwidG90YWxSZWNvcmRzIiwibGVuZ3RoIiwic2FtcGxlUmVjb3JkIiwiYWxsUmVjb3JkcyIsIm9uRXJyb3IiLCJlcnJvciIsInF1ZXJ5Q29tcGxldGVkVHJhaW5pbmciLCJjb21wbGV0ZWRMb2FkaW5nIiwicmVhZFRyYWluaW5nU2Vzc2lvbnMiLCJsb2FkVHJhaW5pbmdTZXNzaW9uRHVlcyIsImZpbHRlciIsImR1ZXNGaWx0ZXIiLCJtZW1iZXJJRCIsImVxIiwidmVzc2VsSUQiLCJ0cmFpbmluZ1R5cGVzIiwidHJhaW5pbmdUeXBlSUQiLCJpZCIsImNvbnRhaW5zIiwibWVtYmVycyIsImRhdGUiLCJkdWVEYXRlIiwibmUiLCJ2YXJpYWJsZXMiLCJsb2FkVHJhaW5pbmdMaXN0Iiwic3RhcnRQYWdlIiwic2VhcmNoRmlsdGVyIiwiY29tcGxldGVkRmlsdGVyIiwib2Zmc2V0IiwidW5pZmllZERhdGEiLCJ0cmFpbmluZ1Nlc3Npb25EdWVzQ291bnQiLCJjb21wbGV0ZWRUcmFpbmluZ0xpc3RDb3VudCIsInNhbXBsZUR1ZSIsInNhbXBsZUNvbXBsZXRlZCIsInJlc3VsdCIsImNhdGVnb3J5QnJlYWtkb3duIiwicmVkdWNlIiwiYWNjIiwiaXRlbSIsImNhdGVnb3J5Iiwic2V0RmlsdGVyIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwiZmlsdGVyZWREYXRhIiwiaW5pdGlhbEZpbHRlciIsImxvYWREYXRhIiwiZiIsInN0YXRzIiwiaGFzT3ZlcmR1ZVVwY29taW5nRGF0YSIsImhhc0NvbXBsZXRlZERhdGEiLCJnZXRMb2FkaW5nU3RhdHVzIiwic3RhdHVzZXMiLCJwdXNoIiwiTG9hZGluZ0luZGljYXRvciIsInN0YXR1cyIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJqb2luIiwiUGFydGlhbExvYWRpbmdJbmRpY2F0b3IiLCJzcGFuIiwiZXJyb3JNZXNzYWdlIiwib25DaGFuZ2UiLCJzaG93VG9vbGJhciIsInBhZ2VTaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});