"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false, excludeFilters = [\n        \"overdueToggle\"\n    ] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Always include completed in unified view\n    ;\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Use training filters hook\n    const { filter, setFilter, handleFilterChange } = useTrainingFilters({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: ()=>{}\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setIsLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const f = {\n            ...filter\n        };\n        if (memberId && +memberId > 0) {\n            f.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        setFilter(f);\n        loadData();\n    }, []) // Only run on mount\n    ;\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.getTrainingDataStats)(unifiedData);\n    }, [\n        unifiedData\n    ]);\n    // Enhanced loading state management\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 255,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 254,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 274,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 273,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 290,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 292,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrainingListFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange,\n                    overdueSwitcher: false,\n                    excludeFilters: excludeFilters\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 299,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                        children: \"Crew Training Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"Unified view of all training activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        trainingSessionDues: trainingSessionDues,\n                                        completedTrainingList: completedTrainingList,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                trainingSessionDues: trainingSessionDues,\n                                completedTrainingList: completedTrainingList,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 21\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 308,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 297,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"ElABiIs4004b/Mc/0TJ2358Yvns=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});