"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainingTypes = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainingTypes = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data provided for filtering\");\n            return [];\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering unified data:\", {\n            totalRecords: unifiedData.length,\n            activeFilters: Object.keys(filter).filter((key)=>filter[key] !== undefined),\n            filterValues: filter\n        });\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (filter.category && filter.category !== \"all\") {\n                if (item.category !== filter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (filter.vesselID) {\n                if (filter.vesselID.eq && item.vesselID !== filter.vesselID.eq) {\n                    return false;\n                }\n                if (filter.vesselID.in && !filter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (filter.trainingTypes) {\n                var _item_trainingType, _filter_trainingTypes_id, _filter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                if (((_filter_trainingTypes_id = filter.trainingTypes.id) === null || _filter_trainingTypes_id === void 0 ? void 0 : _filter_trainingTypes_id.contains) && trainingTypeId !== filter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_filter_trainingTypes_id1 = filter.trainingTypes.id) === null || _filter_trainingTypes_id1 === void 0 ? void 0 : _filter_trainingTypes_id1.in) && !filter.trainingTypes.id.in.includes(trainingTypeId)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (filter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _filter_trainer_id, _filter_trainer_id1;\n                    if (((_filter_trainer_id = filter.trainer.id) === null || _filter_trainer_id === void 0 ? void 0 : _filter_trainer_id.eq) && trainerId !== filter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_filter_trainer_id1 = filter.trainer.id) === null || _filter_trainer_id1 === void 0 ? void 0 : _filter_trainer_id1.in) && !filter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (filter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (filter.members && item.members) {\n                var _filter_members_id, _filter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_filter_members_id = filter.members.id) === null || _filter_members_id === void 0 ? void 0 : _filter_members_id.eq) && !memberIds.includes(filter.members.id.eq)) {\n                    return false;\n                }\n                if ((_filter_members_id1 = filter.members.id) === null || _filter_members_id1 === void 0 ? void 0 : _filter_members_id1.in) {\n                    const hasMatchingMember = filter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (filter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (filter.date.gte && itemDate < filter.date.gte) {\n                    return false;\n                }\n                if (filter.date.lte && itemDate > filter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            categoryBreakdown: filtered.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return filtered;\n    }, [\n        unifiedData,\n        filter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});