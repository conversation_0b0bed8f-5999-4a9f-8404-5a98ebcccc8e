import { useCallback, useState, useMemo, useRef, useEffect } from 'react'
import { UnifiedTrainingData } from '../utils/crew-training-utils'

// Performance optimization: Cache filter results to avoid redundant calculations
const filterCache = new Map<string, UnifiedTrainingData[]>()
const CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues

// Helper function to generate cache key from filter and data
const generateCacheKey = (filter: UnifiedSearchFilter, dataLength: number, dataHash: string): string => {
    return JSON.stringify({ filter, dataLength, dataHash })
}

// Helper function to generate a simple hash from data array
const generateDataHash = (data: UnifiedTrainingData[]): string => {
    if (!data || data.length === 0) return 'empty'
    // Use first and last item IDs plus length for a simple hash
    return `${data[0]?.id}-${data[data.length - 1]?.id}-${data.length}`
}

export interface UnifiedSearchFilter {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

/**
 * Hook for filtering unified training data on the client side
 * Works with merged training data from mergeAndSortCrewTrainingData
 */
export function useUnifiedTrainingFilters(opts: {
    initialFilter: UnifiedSearchFilter
    unifiedData: UnifiedTrainingData[]
}) {
    const { initialFilter, unifiedData } = opts
    const [filter, setFilter] = useState<UnifiedSearchFilter>(initialFilter)
    const [debouncedFilter, setDebouncedFilter] = useState<UnifiedSearchFilter>(initialFilter)
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Debounce filter changes to improve performance during rapid changes
    useEffect(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current)
        }

        debounceTimeoutRef.current = setTimeout(() => {
            setDebouncedFilter(filter)
        }, 300) // 300ms debounce delay

        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current)
            }
        }
    }, [filter])

    const handleFilterChange = useCallback(
        ({ type, data }: { type: string; data: any }) => {
            const next: UnifiedSearchFilter = { ...filter }

            /* ---- vessel ------------------------------------------------------- */
            if (type === 'vessel') {
                if (Array.isArray(data) && data.length) {
                    next.vesselID = { in: data.map((d) => +d.value) }
                } else if (data && !Array.isArray(data)) {
                    next.vesselID = { eq: +data.value }
                } else {
                    delete next.vesselID
                }
            }

            /* ---- trainingType ------------------------------------------------- */
            if (type === 'trainingType') {
                if (Array.isArray(data) && data.length) {
                    next.trainingTypes = { id: { in: data.map((d) => +d.value) } }
                } else if (data && !Array.isArray(data)) {
                    next.trainingTypes = { id: { contains: +data.value } }
                } else {
                    delete next.trainingTypes
                }
            }

            /* ---- trainer ------------------------------------------------------ */
            if (type === 'trainer') {
                if (Array.isArray(data) && data.length) {
                    next.trainer = { id: { in: data.map((d) => +d.value) } }
                } else if (data && !Array.isArray(data)) {
                    next.trainer = { id: { eq: +data.value } }
                } else {
                    delete next.trainer
                }
            }

            /* ---- member ------------------------------------------------------- */
            if (type === 'member') {
                if (Array.isArray(data) && data.length) {
                    next.members = { id: { in: data.map((d) => +d.value) } }
                } else if (data && !Array.isArray(data)) {
                    next.members = { id: { eq: +data.value } }
                } else {
                    delete next.members
                }
            }

            /* ---- dateRange ---------------------------------------------------- */
            if (type === 'dateRange') {
                if (data?.startDate && data?.endDate) {
                    next.date = { gte: data.startDate, lte: data.endDate }
                } else {
                    delete next.date
                }
            }

            /* ---- category ----------------------------------------------------- */
            if (type === 'category') {
                if (data && data !== 'all') {
                    next.category = data
                } else {
                    delete next.category
                }
            }

            setFilter(next)
        },
        [filter],
    )

    // Performance-optimized client-side filtering of unified data
    const filteredData = useMemo(() => {
        if (!unifiedData || !Array.isArray(unifiedData)) {
            console.log('🔍 [useUnifiedTrainingFilters] No unified data provided for filtering')
            return []
        }

        // Performance optimization: Check cache first
        const dataHash = generateDataHash(unifiedData)
        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)

        if (filterCache.has(cacheKey)) {
            console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')
            return filterCache.get(cacheKey)!
        }

        console.log('🔍 [useUnifiedTrainingFilters] Filtering unified data:', {
            totalRecords: unifiedData.length,
            activeFilters: Object.keys(debouncedFilter).filter(key => debouncedFilter[key as keyof UnifiedSearchFilter] !== undefined),
            filterValues: debouncedFilter,
            cacheKey: cacheKey.substring(0, 100) + '...' // Truncate for logging
        })

        const startTime = performance.now()

        // Optimized filtering with early returns for better performance
        const filtered = unifiedData.filter((item: UnifiedTrainingData) => {
            // Category filter
            if (debouncedFilter.category && debouncedFilter.category !== 'all') {
                if (item.category !== debouncedFilter.category) {
                    return false
                }
            }

            // Vessel filter
            if (debouncedFilter.vesselID) {
                if (debouncedFilter.vesselID.eq && item.vesselID !== debouncedFilter.vesselID.eq) {
                    return false
                }
                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(item.vesselID)) {
                    return false
                }
            }

            // Training type filter
            if (debouncedFilter.trainingTypes) {
                const trainingTypeId = item.trainingTypeID || item.trainingType?.id
                if (debouncedFilter.trainingTypes.id?.contains && trainingTypeId !== debouncedFilter.trainingTypes.id.contains) {
                    return false
                }
                if (debouncedFilter.trainingTypes.id?.in && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeId)) {
                    return false
                }
            }

            // Trainer filter (for completed training sessions)
            if (debouncedFilter.trainer && item.originalData) {
                const trainerId = item.originalData.trainerID || item.originalData.trainer?.id
                if (trainerId) {
                    if (debouncedFilter.trainer.id?.eq && trainerId !== debouncedFilter.trainer.id.eq) {
                        return false
                    }
                    if (debouncedFilter.trainer.id?.in && !debouncedFilter.trainer.id.in.includes(trainerId)) {
                        return false
                    }
                } else if (debouncedFilter.trainer.id) {
                    // If trainer filter is applied but no trainer data exists, exclude this item
                    return false
                }
            }

            // Member filter
            if (debouncedFilter.members && item.members) {
                const memberIds = item.members.map((member: any) => member.id)
                if (debouncedFilter.members.id?.eq && !memberIds.includes(debouncedFilter.members.id.eq)) {
                    return false
                }
                if (debouncedFilter.members.id?.in) {
                    const hasMatchingMember = debouncedFilter.members.id.in.some(id => memberIds.includes(id))
                    if (!hasMatchingMember) {
                        return false
                    }
                }
            }

            // Date filter
            if (debouncedFilter.date && item.dueDate) {
                const itemDate = new Date(item.dueDate)
                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {
                    return false
                }
                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {
                    return false
                }
            }

            return true
        })

        const endTime = performance.now()
        const filterTime = endTime - startTime

        console.log('🔍 [useUnifiedTrainingFilters] Filtering complete:', {
            originalCount: unifiedData.length,
            filteredCount: filtered.length,
            filterTime: `${filterTime.toFixed(2)}ms`,
            categoryBreakdown: filtered.reduce((acc: any, item: any) => {
                acc[item.category] = (acc[item.category] || 0) + 1
                return acc
            }, {})
        })

        // Performance optimization: Cache the result
        if (filterCache.size >= CACHE_SIZE_LIMIT) {
            // Remove oldest entries when cache is full
            const firstKey = filterCache.keys().next().value
            if (firstKey) {
                filterCache.delete(firstKey)
            }
        }
        filterCache.set(cacheKey, filtered)

        return filtered
    }, [unifiedData, debouncedFilter])

    return {
        filter,
        setFilter,
        handleFilterChange,
        filteredData
    }
}
