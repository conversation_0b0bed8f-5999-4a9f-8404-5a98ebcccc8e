"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filter/unified-training-filter */ \"(app-pages-browser)/./src/components/filter/unified-training-filter.tsx\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const includeCompleted = true // Always include completed in unified view\n    ;\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const startTime = performance.now();\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        const endTime = performance.now();\n        const mergeTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            mergeTime: \"\".concat(mergeTime.toFixed(2), \"ms\"),\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Simplified data loading without server-side filtering\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        // Load training session dues without filters (client-side filtering will handle this)\n        const duesFilter = {};\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (memberId && +memberId > 0) {\n            duesFilter.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        await loadTrainingSessionDues(duesFilter);\n        // Load completed training without filters (client-side filtering will handle this)\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        await loadTrainingList(0, completedFilter);\n        setIsLoading(false);\n    }, [\n        vesselId,\n        memberId,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Enhanced loading state management\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 253,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 252,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 272,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 271,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 288,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 290,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__.UnifiedTrainingFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 297,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                        children: \"Crew Training Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"Unified view of all training activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        unifiedData: filteredData,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                unifiedData: filteredData,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 21\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 304,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 295,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"5GdiogSM/IrhyxZ75aAQBzudabo=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy91bmlmaWVkLXRyYWluaW5nLWV4YW1wbGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVpRTtBQUNwQjtBQUliO0FBQytCO0FBQ0M7QUFDMUI7QUFDUztBQUNnRDtBQUNaO0FBQ047QUFDTDtBQUNyQztBQUNHO0FBUS9CLE1BQU1rQix5QkFBeUI7UUFBQyxFQUNuQ0MsV0FBVyxDQUFDLEVBQ1pDLFdBQVcsQ0FBQyxFQUNaQyxlQUFlLEtBQUssRUFDTTs7SUFDMUIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN3QixxQkFBcUJDLHVCQUF1QixHQUFHekIsK0NBQVFBLENBQVEsRUFBRTtJQUN4RSxNQUFNLENBQUMwQix1QkFBdUJDLHlCQUF5QixHQUFHM0IsK0NBQVFBLENBQzlELEVBQUU7SUFFTixNQUFNNEIsbUJBQW1CLEtBQUssMkNBQTJDOztJQUV6RSxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRzlCLCtDQUFRQSxDQUFNO0lBRXBELE1BQU0sRUFBRStCLGlCQUFpQixFQUFFLEdBQUd2Qiw4RUFBaUJBO0lBRS9DLHlCQUF5QjtJQUN6QlAsZ0RBQVNBLENBQUM7UUFDTjZCLGVBQWVoQixvRUFBY0E7SUFDakMsR0FBRyxFQUFFO0lBRUwscURBQXFEO0lBQ3JELE1BQU0sQ0FBQ2tCLDBCQUEwQixFQUFFQyxTQUFTQyxXQUFXLEVBQUUsQ0FBQyxHQUFHOUIsNkRBQVlBLENBQ3JFRSw4RUFBMEJBLEVBQzFCO1FBQ0k2QixhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTRSx1QkFBdUIsQ0FBQ0MsS0FBSyxJQUFJLEVBQUU7WUFFekRDLFFBQVFDLEdBQUcsQ0FDUCx5RUFDQTtnQkFDSUMsY0FBY0wsS0FBS00sTUFBTTtnQkFDekJDLGNBQWNQLElBQUksQ0FBQyxFQUFFO2dCQUNyQlEsWUFBWVI7WUFDaEI7WUFHSmIsdUJBQXVCYTtRQUMzQjtRQUNBUyxTQUFTLENBQUNDO1lBQ05QLFFBQVFPLEtBQUssQ0FBQyx3Q0FBd0NBO1FBQzFEO0lBQ0o7SUFHSix3Q0FBd0M7SUFDeEMsTUFBTSxDQUFDQyx3QkFBd0IsRUFBRWhCLFNBQVNpQixnQkFBZ0IsRUFBRSxDQUFDLEdBQ3pEOUMsNkRBQVlBLENBQUNDLHFFQUFpQkEsRUFBRTtRQUM1QjhCLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNjLG9CQUFvQixDQUFDWCxLQUFLLElBQUksRUFBRTtZQUV0REMsUUFBUUMsR0FBRyxDQUNQLCtFQUNBO2dCQUNJQyxjQUFjTCxLQUFLTSxNQUFNO2dCQUN6QkMsY0FBY1AsSUFBSSxDQUFDLEVBQUU7Z0JBQ3JCUSxZQUFZUjtZQUNoQjtZQUdKWCx5QkFBeUJXO1FBQzdCO1FBQ0FTLFNBQVMsQ0FBQ0M7WUFDTlAsUUFBUU8sS0FBSyxDQUFDLHFDQUFxQ0E7UUFDdkQ7SUFDSjtJQUVKLHNDQUFzQztJQUN0QyxNQUFNSSwwQkFBMEJqRCxrREFBV0EsQ0FDdkMsT0FBT2tEO1FBQ0gsTUFBTUMsYUFBa0IsQ0FBQztRQUN6QixJQUFJbEMsWUFBWUEsV0FBVyxHQUFHO1lBQzFCa0MsV0FBV0MsUUFBUSxHQUFHO2dCQUFFQyxJQUFJLENBQUNwQztZQUFTO1FBQzFDO1FBQ0EsSUFBSUQsWUFBWUEsV0FBVyxHQUFHO1lBQzFCbUMsV0FBV0csUUFBUSxHQUFHO2dCQUFFRCxJQUFJLENBQUNyQztZQUFTO1FBQzFDO1FBQ0EsSUFBSWtDLE9BQU9JLFFBQVEsRUFBRTtZQUNqQkgsV0FBV0csUUFBUSxHQUFHSixPQUFPSSxRQUFRO1FBQ3pDO1FBQ0EsSUFBSUosT0FBT0ssYUFBYSxFQUFFO1lBQ3RCSixXQUFXSyxjQUFjLEdBQUc7Z0JBQ3hCSCxJQUFJSCxPQUFPSyxhQUFhLENBQUNFLEVBQUUsQ0FBQ0MsUUFBUTtZQUN4QztRQUNKO1FBQ0EsSUFBSVIsT0FBT1MsT0FBTyxFQUFFO1lBQ2hCUixXQUFXQyxRQUFRLEdBQUc7Z0JBQUVDLElBQUlILE9BQU9TLE9BQU8sQ0FBQ0YsRUFBRSxDQUFDQyxRQUFRO1lBQUM7UUFDM0Q7UUFDQSxJQUFJUixPQUFPVSxJQUFJLEVBQUU7WUFDYlQsV0FBV1UsT0FBTyxHQUFHWCxPQUFPVSxJQUFJO1FBQ3BDLE9BQU87WUFDSFQsV0FBV1UsT0FBTyxHQUFHO2dCQUFFQyxJQUFJO1lBQUs7UUFDcEM7UUFFQSxNQUFNakMseUJBQXlCO1lBQzNCa0MsV0FBVztnQkFDUGIsUUFBUUM7WUFDWjtRQUNKO0lBQ0osR0FDQTtRQUFDbEM7UUFBVUQ7UUFBVWE7S0FBeUI7SUFHbEQsbUNBQW1DO0lBQ25DLE1BQU1tQyxtQkFBbUJoRSxrREFBV0EsQ0FDaEM7WUFBT2lFLDZFQUFvQixHQUFHQyxnRkFBb0IsQ0FBQztRQUMvQyxNQUFNQyxrQkFBdUIsQ0FBQztRQUM5QixJQUFJbkQsWUFBWUEsV0FBVyxHQUFHO1lBQzFCbUQsZ0JBQWdCYixRQUFRLEdBQUc7Z0JBQUVELElBQUksQ0FBQ3JDO1lBQVM7UUFDL0M7UUFDQSxJQUFJa0QsYUFBYVosUUFBUSxFQUFFO1lBQ3ZCYSxnQkFBZ0JiLFFBQVEsR0FBR1ksYUFBYVosUUFBUTtRQUNwRDtRQUVBLE1BQU1SLHVCQUF1QjtZQUN6QmlCLFdBQVc7Z0JBQ1BiLFFBQVFpQjtnQkFDUkMsUUFBUUgsWUFBWTtnQkFDcEJJLE9BQU87WUFDWDtRQUNKO0lBQ0osR0FDQTtRQUFDckQ7UUFBVThCO0tBQXVCO0lBR3RDLDZFQUE2RTtJQUM3RSxNQUFNd0IsY0FBY3ZFLDhDQUFPQSxDQUFDO1FBQ3hCLE1BQU13RSxZQUFZQyxZQUFZQyxHQUFHO1FBRWpDbkMsUUFBUUMsR0FBRyxDQUNQLHNGQUNBO1lBQ0ltQywwQkFBMEJyRCxDQUFBQSxnQ0FBQUEsMENBQUFBLG9CQUFxQm9CLE1BQU0sS0FBSTtZQUN6RGtDLDRCQUE0QnBELENBQUFBLGtDQUFBQSw0Q0FBQUEsc0JBQXVCa0IsTUFBTSxLQUFJO1lBQzdEaEI7WUFDQW1ELFNBQVMsRUFBRXZELGdDQUFBQSwwQ0FBQUEsbUJBQXFCLENBQUMsRUFBRTtZQUNuQ3dELGVBQWUsRUFBRXRELGtDQUFBQSw0Q0FBQUEscUJBQXVCLENBQUMsRUFBRTtRQUMvQztRQUdKLE1BQU11RCxTQUFTdEUsNkdBQTRCQSxDQUFDO1lBQ3hDYTtZQUNBRTtZQUNBSztZQUNBSDtRQUNKO1FBRUEsTUFBTXNELFVBQVVQLFlBQVlDLEdBQUc7UUFDL0IsTUFBTU8sWUFBWUQsVUFBVVI7UUFFNUJqQyxRQUFRQyxHQUFHLENBQ1AsbUZBQ0E7WUFDSUMsY0FBY3NDLE9BQU9yQyxNQUFNO1lBQzNCdUMsV0FBVyxHQUF3QixPQUFyQkEsVUFBVUMsT0FBTyxDQUFDLElBQUc7WUFDbkNDLG1CQUFtQkosT0FBT0ssTUFBTSxDQUFDLENBQUNDLEtBQVVDO2dCQUN4Q0QsR0FBRyxDQUFDQyxLQUFLQyxRQUFRLENBQUMsR0FBRyxDQUFDRixHQUFHLENBQUNDLEtBQUtDLFFBQVEsQ0FBQyxJQUFJLEtBQUs7Z0JBQ2pELE9BQU9GO1lBQ1gsR0FBRyxDQUFDO1lBQ0oxQyxjQUFjb0MsTUFBTSxDQUFDLEVBQUU7WUFDdkJuQyxZQUFZbUM7UUFDaEI7UUFHSixPQUFPQTtJQUNYLEdBQUc7UUFDQ3pEO1FBQ0FFO1FBQ0FLO1FBQ0FIO0tBQ0g7SUFFRCwrREFBK0Q7SUFDL0QsTUFBTSxFQUFFOEQsa0JBQWtCLEVBQUVDLFlBQVksRUFBRSxHQUFHOUUsMkZBQXlCQSxDQUFDO1FBQ25FK0UsZUFBZSxDQUFDO1FBQ2hCbkI7SUFDSjtJQUVBLHdEQUF3RDtJQUN4RCxNQUFNb0IsV0FBVzFGLGtEQUFXQSxDQUFDO1FBQ3pCb0IsYUFBYTtRQUViLHNGQUFzRjtRQUN0RixNQUFNK0IsYUFBa0IsQ0FBQztRQUN6QixJQUFJbkMsWUFBWUEsV0FBVyxHQUFHO1lBQzFCbUMsV0FBV0csUUFBUSxHQUFHO2dCQUFFRCxJQUFJLENBQUNyQztZQUFTO1FBQzFDO1FBQ0EsSUFBSUMsWUFBWSxDQUFDQSxXQUFXLEdBQUc7WUFDM0JrQyxXQUFXUSxPQUFPLEdBQUc7Z0JBQUVGLElBQUk7b0JBQUVDLFVBQVUsQ0FBQ3pDO2dCQUFTO1lBQUU7UUFDdkQ7UUFDQSxNQUFNZ0Msd0JBQXdCRTtRQUU5QixtRkFBbUY7UUFDbkYsTUFBTWdCLGtCQUF1QixDQUFDO1FBQzlCLElBQUluRCxZQUFZQSxXQUFXLEdBQUc7WUFDMUJtRCxnQkFBZ0JiLFFBQVEsR0FBRztnQkFBRUQsSUFBSSxDQUFDckM7WUFBUztRQUMvQztRQUNBLE1BQU1nRCxpQkFBaUIsR0FBR0c7UUFFMUIvQyxhQUFhO0lBQ2pCLEdBQUc7UUFBQ0o7UUFBVUM7UUFBVWdDO1FBQXlCZTtLQUFpQjtJQUVsRSwrQkFBK0I7SUFDL0JsRSxnREFBU0EsQ0FBQztRQUNONEY7SUFDSixHQUFHO1FBQUNBO0tBQVM7SUFFYixvQ0FBb0M7SUFDcEMsTUFBTUMseUJBQ0Z0RSx1QkFBdUJBLG9CQUFvQm9CLE1BQU0sR0FBRztJQUN4RCxNQUFNbUQsbUJBQ0ZyRSx5QkFBeUJBLHNCQUFzQmtCLE1BQU0sR0FBRztJQUU1RCxpQ0FBaUM7SUFDakMsTUFBTW9ELG1CQUFtQjtRQUNyQixNQUFNQyxXQUFXLEVBQUU7UUFDbkIsSUFBSS9ELGFBQWErRCxTQUFTQyxJQUFJLENBQUM7UUFDL0IsSUFBSWhELGtCQUFrQitDLFNBQVNDLElBQUksQ0FBQztRQUNwQyxJQUFJNUUsV0FBVzJFLFNBQVNDLElBQUksQ0FBQztRQUM3QixPQUFPRDtJQUNYO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU1FLG1CQUFtQjtZQUFDLEVBQUVDLE1BQU0sRUFBd0I7NkJBQ3RELDhEQUFDQztZQUFJQyxXQUFVO3NCQUNYLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUNyRixvRkFBT0E7d0JBQUNxRixXQUFVOzs7Ozs7a0NBQ25CLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ1gsOERBQUNDO2dDQUFFRCxXQUFVOzBDQUFzQjs7Ozs7OzRCQUdsQ0YsT0FBT3hELE1BQU0sR0FBRyxtQkFDYiw4REFBQzJEO2dDQUFFRCxXQUFVOztvQ0FBZ0M7b0NBQ3JCRixPQUFPSSxJQUFJLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFReEQsNERBQTREO0lBQzVELE1BQU1DLDBCQUEwQjtZQUFDLEVBQUVMLE1BQU0sRUFBd0I7NkJBQzdELDhEQUFDQztZQUFJQyxXQUFVO3NCQUNYLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUNyRixvRkFBT0E7d0JBQUNxRixXQUFVOzs7Ozs7a0NBQ25CLDhEQUFDSTs7NEJBQUs7NEJBQVNOLE9BQU9JLElBQUksQ0FBQzs0QkFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUs3QyxvQkFBb0I7SUFDcEIsSUFDSSxDQUFDM0UsZUFDQSxDQUFDZCx1RUFBYUEsQ0FBQyxpQkFBaUJjLGdCQUM3QixDQUFDZCx1RUFBYUEsQ0FBQyxpQkFBaUJjLGdCQUNoQyxDQUFDZCx1RUFBYUEsQ0FBQyxtQkFBbUJjLGdCQUNsQyxDQUFDZCx1RUFBYUEsQ0FBQyx3QkFBd0JjLGNBQzdDO1FBQ0UsT0FBTyxDQUFDQSw0QkFDSiw4REFBQ2IscURBQU9BOzs7O3NDQUVSLDhEQUFDQSxxREFBT0E7WUFBQzJGLGNBQWE7Ozs7OztJQUU5QjtJQUVBLHFCQUNJLDhEQUFDTjtRQUFJQyxXQUFVOzswQkFFWCw4REFBQzdGLGdEQUFJQTswQkFDRCw0RUFBQ0csNkZBQXFCQTtvQkFDbEJRLFVBQVVBO29CQUNWd0YsVUFBVWxCOzs7Ozs7Ozs7OzswQkFJbEIsOERBQUNqRixnREFBSUE7Z0JBQUM2RixXQUFVOzBCQUNaLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRVgsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNYLDRFQUFDRDs7a0RBQ0csOERBQUMzRix5REFBRUE7a0RBQUM7Ozs7OztrREFDSiw4REFBQzZGO3dDQUFFRCxXQUFVO2tEQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTFELDhEQUFDRDtzQ0FDSS9FLGFBQ0QsQ0FBQ3dFLDBCQUNELENBQUNDLG1CQUNHLCtDQUErQzswQ0FDL0MsOERBQUNJO2dDQUFpQkMsUUFBUUo7Ozs7OzRDQUMxQjFFLGFBQ0R3RSxDQUFBQSwwQkFBMEJDLGdCQUFlLElBQ3hDLG9EQUFvRDswQ0FDcEQsOERBQUNNO2dDQUFJQyxXQUFVOztrREFDWCw4REFBQy9GLHlFQUFvQkE7d0NBQ2pCa0UsYUFBYWtCO3dDQUNiNUQsbUJBQW1CQTt3Q0FDbkJILGtCQUFrQkE7d0NBQ2xCUixVQUFVQTt3Q0FDVkMsY0FBY0E7d0NBQ2R3RixhQUFhO3dDQUNiQyxVQUFVOzs7Ozs7a0RBRWQsOERBQUNMO3dDQUNHTCxRQUFRSjs7Ozs7Ozs7Ozs7NENBSWhCLHlCQUF5QjswQ0FDekIsOERBQUN6Rix5RUFBb0JBO2dDQUNqQmtFLGFBQWFrQjtnQ0FDYjVELG1CQUFtQkE7Z0NBQ25CSCxrQkFBa0JBO2dDQUNsQlIsVUFBVUE7Z0NBQ1ZDLGNBQWNBO2dDQUNkd0YsYUFBYTtnQ0FDYkMsVUFBVTs7Ozs7Ozs7Ozs7d0JBTXJCeEYsYUFDSXdFLENBQUFBLDBCQUEwQkMsZ0JBQWUsbUJBQ3RDLDhEQUFDTTs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ0M7MENBQUU7Ozs7Ozs7Ozs7O3dCQVNkLENBQUNqRixhQUFhbUQsWUFBWTdCLE1BQU0sS0FBSyxtQkFDbEMsOERBQUN5RDs0QkFBSUMsV0FBVTs7OENBQ1gsOERBQUNDO29DQUFFRCxXQUFVOzhDQUFVOzs7Ozs7OENBR3ZCLDhEQUFDQztvQ0FBRUQsV0FBVTs4Q0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTeEQsRUFBQztHQWxXWXBGOztRQWNxQlYsMEVBQWlCQTtRQVFjSix5REFBWUE7UUEwQnJFQSx5REFBWUE7UUErSDZCUyx1RkFBeUJBOzs7S0EvSzdESztBQW9XYiwrREFBZUEsc0JBQXNCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy91bmlmaWVkLXRyYWluaW5nLWV4YW1wbGUudHN4P2EyN2MiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VMYXp5UXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCdcbmltcG9ydCB7XG4gICAgVFJBSU5JTkdfU0VTU0lPTlMsXG4gICAgUkVBRF9UUkFJTklOR19TRVNTSU9OX0RVRVMsXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xuaW1wb3J0IHsgVW5pZmllZFRyYWluaW5nVGFibGUgfSBmcm9tICcuL3VuaWZpZWQtdHJhaW5pbmctdGFibGUnXG5pbXBvcnQgeyB1c2VWZXNzZWxJY29uRGF0YSB9IGZyb20gJ0AvYXBwL2xpYi92ZXNzZWwtaWNvbi1oZWxwZXInXG5pbXBvcnQgeyBDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpJ1xuaW1wb3J0IHsgSDIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdHlwb2dyYXBoeSdcbmltcG9ydCB7IG1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGEgfSBmcm9tICdAL2FwcC91aS9jcmV3LXRyYWluaW5nL3V0aWxzL2NyZXctdHJhaW5pbmctdXRpbHMnXG5pbXBvcnQgeyBVbmlmaWVkVHJhaW5pbmdGaWx0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyL3VuaWZpZWQtdHJhaW5pbmctZmlsdGVyJ1xuaW1wb3J0IHsgdXNlVW5pZmllZFRyYWluaW5nRmlsdGVycyB9IGZyb20gJy4vaG9va3MvdXNlVW5pZmllZFRyYWluaW5nRmlsdGVycydcbmltcG9ydCB7IGdldFBlcm1pc3Npb25zLCBoYXNQZXJtaXNzaW9uIH0gZnJvbSAnQC9hcHAvaGVscGVycy91c2VySGVscGVyJ1xuaW1wb3J0IExvYWRpbmcgZnJvbSAnQC9hcHAvbG9hZGluZydcbmltcG9ydCB7IExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmludGVyZmFjZSBVbmlmaWVkVHJhaW5pbmdFeGFtcGxlUHJvcHMge1xuICAgIHZlc3NlbElkPzogbnVtYmVyXG4gICAgbWVtYmVySWQ/OiBudW1iZXJcbiAgICBpc1Zlc3NlbFZpZXc/OiBib29sZWFuXG59XG5cbmV4cG9ydCBjb25zdCBVbmlmaWVkVHJhaW5pbmdFeGFtcGxlID0gKHtcbiAgICB2ZXNzZWxJZCA9IDAsXG4gICAgbWVtYmVySWQgPSAwLFxuICAgIGlzVmVzc2VsVmlldyA9IGZhbHNlLFxufTogVW5pZmllZFRyYWluaW5nRXhhbXBsZVByb3BzKSA9PiB7XG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gICAgY29uc3QgW3RyYWluaW5nU2Vzc2lvbkR1ZXMsIHNldFRyYWluaW5nU2Vzc2lvbkR1ZXNdID0gdXNlU3RhdGU8YW55W10+KFtdKVxuICAgIGNvbnN0IFtjb21wbGV0ZWRUcmFpbmluZ0xpc3QsIHNldENvbXBsZXRlZFRyYWluaW5nTGlzdF0gPSB1c2VTdGF0ZTxhbnlbXT4oXG4gICAgICAgIFtdLFxuICAgIClcbiAgICBjb25zdCBpbmNsdWRlQ29tcGxldGVkID0gdHJ1ZSAvLyBBbHdheXMgaW5jbHVkZSBjb21wbGV0ZWQgaW4gdW5pZmllZCB2aWV3XG5cbiAgICBjb25zdCBbcGVybWlzc2lvbnMsIHNldFBlcm1pc3Npb25zXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXG5cbiAgICBjb25zdCB7IGdldFZlc3NlbFdpdGhJY29uIH0gPSB1c2VWZXNzZWxJY29uRGF0YSgpXG5cbiAgICAvLyBJbml0aWFsaXplIHBlcm1pc3Npb25zXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgc2V0UGVybWlzc2lvbnMoZ2V0UGVybWlzc2lvbnMpXG4gICAgfSwgW10pXG5cbiAgICAvLyBRdWVyeSBmb3IgdHJhaW5pbmcgc2Vzc2lvbiBkdWVzIChvdmVyZHVlL3VwY29taW5nKVxuICAgIGNvbnN0IFtxdWVyeVRyYWluaW5nU2Vzc2lvbkR1ZXMsIHsgbG9hZGluZzogZHVlc0xvYWRpbmcgfV0gPSB1c2VMYXp5UXVlcnkoXG4gICAgICAgIFJFQURfVFJBSU5JTkdfU0VTU0lPTl9EVUVTLFxuICAgICAgICB7XG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkVHJhaW5pbmdTZXNzaW9uRHVlcy5ub2RlcyB8fCBbXVxuXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgICAgICAgICfwn5SNIFtVbmlmaWVkVHJhaW5pbmdFeGFtcGxlXSBSYXcgdHJhaW5pbmcgc2Vzc2lvbiBkdWVzIGRhdGE6JyxcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgdG90YWxSZWNvcmRzOiBkYXRhLmxlbmd0aCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNhbXBsZVJlY29yZDogZGF0YVswXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsbFJlY29yZHM6IGRhdGEsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgKVxuXG4gICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdTZXNzaW9uRHVlcyhkYXRhKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB0cmFpbmluZyBzZXNzaW9uIGR1ZXM6JywgZXJyb3IpXG4gICAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgIClcblxuICAgIC8vIFF1ZXJ5IGZvciBjb21wbGV0ZWQgdHJhaW5pbmcgc2Vzc2lvbnNcbiAgICBjb25zdCBbcXVlcnlDb21wbGV0ZWRUcmFpbmluZywgeyBsb2FkaW5nOiBjb21wbGV0ZWRMb2FkaW5nIH1dID1cbiAgICAgICAgdXNlTGF6eVF1ZXJ5KFRSQUlOSU5HX1NFU1NJT05TLCB7XG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkVHJhaW5pbmdTZXNzaW9ucy5ub2RlcyB8fCBbXVxuXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgICAgICAgICfwn5SNIFtVbmlmaWVkVHJhaW5pbmdFeGFtcGxlXSBSYXcgY29tcGxldGVkIHRyYWluaW5nIHNlc3Npb25zIGRhdGE6JyxcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgdG90YWxSZWNvcmRzOiBkYXRhLmxlbmd0aCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNhbXBsZVJlY29yZDogZGF0YVswXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsbFJlY29yZHM6IGRhdGEsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgKVxuXG4gICAgICAgICAgICAgICAgc2V0Q29tcGxldGVkVHJhaW5pbmdMaXN0KGRhdGEpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGNvbXBsZXRlZCB0cmFpbmluZzonLCBlcnJvcilcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pXG5cbiAgICAvLyBMb2FkIHRyYWluaW5nIHNlc3Npb24gZHVlcyBmdW5jdGlvblxuICAgIGNvbnN0IGxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jIChmaWx0ZXI6IGFueSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZHVlc0ZpbHRlcjogYW55ID0ge31cbiAgICAgICAgICAgIGlmIChtZW1iZXJJZCAmJiBtZW1iZXJJZCA+IDApIHtcbiAgICAgICAgICAgICAgICBkdWVzRmlsdGVyLm1lbWJlcklEID0geyBlcTogK21lbWJlcklkIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh2ZXNzZWxJZCAmJiB2ZXNzZWxJZCA+IDApIHtcbiAgICAgICAgICAgICAgICBkdWVzRmlsdGVyLnZlc3NlbElEID0geyBlcTogK3Zlc3NlbElkIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChmaWx0ZXIudmVzc2VsSUQpIHtcbiAgICAgICAgICAgICAgICBkdWVzRmlsdGVyLnZlc3NlbElEID0gZmlsdGVyLnZlc3NlbElEXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZmlsdGVyLnRyYWluaW5nVHlwZXMpIHtcbiAgICAgICAgICAgICAgICBkdWVzRmlsdGVyLnRyYWluaW5nVHlwZUlEID0ge1xuICAgICAgICAgICAgICAgICAgICBlcTogZmlsdGVyLnRyYWluaW5nVHlwZXMuaWQuY29udGFpbnMsXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGZpbHRlci5tZW1iZXJzKSB7XG4gICAgICAgICAgICAgICAgZHVlc0ZpbHRlci5tZW1iZXJJRCA9IHsgZXE6IGZpbHRlci5tZW1iZXJzLmlkLmNvbnRhaW5zIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChmaWx0ZXIuZGF0ZSkge1xuICAgICAgICAgICAgICAgIGR1ZXNGaWx0ZXIuZHVlRGF0ZSA9IGZpbHRlci5kYXRlXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGR1ZXNGaWx0ZXIuZHVlRGF0ZSA9IHsgbmU6IG51bGwgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBhd2FpdCBxdWVyeVRyYWluaW5nU2Vzc2lvbkR1ZXMoe1xuICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xuICAgICAgICAgICAgICAgICAgICBmaWx0ZXI6IGR1ZXNGaWx0ZXIsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0sXG4gICAgICAgIFttZW1iZXJJZCwgdmVzc2VsSWQsIHF1ZXJ5VHJhaW5pbmdTZXNzaW9uRHVlc10sXG4gICAgKVxuXG4gICAgLy8gTG9hZCBjb21wbGV0ZWQgdHJhaW5pbmcgZnVuY3Rpb25cbiAgICBjb25zdCBsb2FkVHJhaW5pbmdMaXN0ID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jIChzdGFydFBhZ2U6IG51bWJlciA9IDAsIHNlYXJjaEZpbHRlcjogYW55ID0ge30pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRlZEZpbHRlcjogYW55ID0ge31cbiAgICAgICAgICAgIGlmICh2ZXNzZWxJZCAmJiB2ZXNzZWxJZCA+IDApIHtcbiAgICAgICAgICAgICAgICBjb21wbGV0ZWRGaWx0ZXIudmVzc2VsSUQgPSB7IGVxOiArdmVzc2VsSWQgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHNlYXJjaEZpbHRlci52ZXNzZWxJRCkge1xuICAgICAgICAgICAgICAgIGNvbXBsZXRlZEZpbHRlci52ZXNzZWxJRCA9IHNlYXJjaEZpbHRlci52ZXNzZWxJRFxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBhd2FpdCBxdWVyeUNvbXBsZXRlZFRyYWluaW5nKHtcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiBjb21wbGV0ZWRGaWx0ZXIsXG4gICAgICAgICAgICAgICAgICAgIG9mZnNldDogc3RhcnRQYWdlICogMjAsIC8vIFVzZSAyMCBpdGVtcyBwZXIgcGFnZSB0byBtYXRjaCBwYWdlU2l6ZVxuICAgICAgICAgICAgICAgICAgICBsaW1pdDogMjAsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0sXG4gICAgICAgIFt2ZXNzZWxJZCwgcXVlcnlDb21wbGV0ZWRUcmFpbmluZ10sXG4gICAgKVxuXG4gICAgLy8gTWVtb2l6ZSB0aGUgdW5pZmllZCBkYXRhIGNhbGN1bGF0aW9uIHRvIHByZXZlbnQgdW5uZWNlc3NhcnkgcmVjYWxjdWxhdGlvbnNcbiAgICBjb25zdCB1bmlmaWVkRGF0YSA9IHVzZU1lbW8oKCkgPT4ge1xuICAgICAgICBjb25zdCBzdGFydFRpbWUgPSBwZXJmb3JtYW5jZS5ub3coKVxuXG4gICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgJ/CflI0gW1VuaWZpZWRUcmFpbmluZ0V4YW1wbGVdIElucHV0IGRhdGEgZm9yIG1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGE6JyxcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25EdWVzQ291bnQ6IHRyYWluaW5nU2Vzc2lvbkR1ZXM/Lmxlbmd0aCB8fCAwLFxuICAgICAgICAgICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdENvdW50OiBjb21wbGV0ZWRUcmFpbmluZ0xpc3Q/Lmxlbmd0aCB8fCAwLFxuICAgICAgICAgICAgICAgIGluY2x1ZGVDb21wbGV0ZWQsXG4gICAgICAgICAgICAgICAgc2FtcGxlRHVlOiB0cmFpbmluZ1Nlc3Npb25EdWVzPy5bMF0sXG4gICAgICAgICAgICAgICAgc2FtcGxlQ29tcGxldGVkOiBjb21wbGV0ZWRUcmFpbmluZ0xpc3Q/LlswXSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIClcblxuICAgICAgICBjb25zdCByZXN1bHQgPSBtZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhKHtcbiAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXMsXG4gICAgICAgICAgICBjb21wbGV0ZWRUcmFpbmluZ0xpc3QsXG4gICAgICAgICAgICBnZXRWZXNzZWxXaXRoSWNvbixcbiAgICAgICAgICAgIGluY2x1ZGVDb21wbGV0ZWQsXG4gICAgICAgIH0pXG5cbiAgICAgICAgY29uc3QgZW5kVGltZSA9IHBlcmZvcm1hbmNlLm5vdygpXG4gICAgICAgIGNvbnN0IG1lcmdlVGltZSA9IGVuZFRpbWUgLSBzdGFydFRpbWVcblxuICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICAgICfwn5SNIFtVbmlmaWVkVHJhaW5pbmdFeGFtcGxlXSBSZXN1bHQgZnJvbSBtZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhOicsXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgdG90YWxSZWNvcmRzOiByZXN1bHQubGVuZ3RoLFxuICAgICAgICAgICAgICAgIG1lcmdlVGltZTogYCR7bWVyZ2VUaW1lLnRvRml4ZWQoMil9bXNgLFxuICAgICAgICAgICAgICAgIGNhdGVnb3J5QnJlYWtkb3duOiByZXN1bHQucmVkdWNlKChhY2M6IGFueSwgaXRlbTogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGFjY1tpdGVtLmNhdGVnb3J5XSA9IChhY2NbaXRlbS5jYXRlZ29yeV0gfHwgMCkgKyAxXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBhY2NcbiAgICAgICAgICAgICAgICB9LCB7fSksXG4gICAgICAgICAgICAgICAgc2FtcGxlUmVjb3JkOiByZXN1bHRbMF0sXG4gICAgICAgICAgICAgICAgYWxsUmVjb3JkczogcmVzdWx0LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgKVxuXG4gICAgICAgIHJldHVybiByZXN1bHRcbiAgICB9LCBbXG4gICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXMsXG4gICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCxcbiAgICAgICAgZ2V0VmVzc2VsV2l0aEljb24sXG4gICAgICAgIGluY2x1ZGVDb21wbGV0ZWQsXG4gICAgXSlcblxuICAgIC8vIFVzZSB1bmlmaWVkIHRyYWluaW5nIGZpbHRlcnMgaG9vayB3aXRoIGNsaWVudC1zaWRlIGZpbHRlcmluZ1xuICAgIGNvbnN0IHsgaGFuZGxlRmlsdGVyQ2hhbmdlLCBmaWx0ZXJlZERhdGEgfSA9IHVzZVVuaWZpZWRUcmFpbmluZ0ZpbHRlcnMoe1xuICAgICAgICBpbml0aWFsRmlsdGVyOiB7fSxcbiAgICAgICAgdW5pZmllZERhdGEsXG4gICAgfSlcblxuICAgIC8vIFNpbXBsaWZpZWQgZGF0YSBsb2FkaW5nIHdpdGhvdXQgc2VydmVyLXNpZGUgZmlsdGVyaW5nXG4gICAgY29uc3QgbG9hZERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIHNldElzTG9hZGluZyh0cnVlKVxuXG4gICAgICAgIC8vIExvYWQgdHJhaW5pbmcgc2Vzc2lvbiBkdWVzIHdpdGhvdXQgZmlsdGVycyAoY2xpZW50LXNpZGUgZmlsdGVyaW5nIHdpbGwgaGFuZGxlIHRoaXMpXG4gICAgICAgIGNvbnN0IGR1ZXNGaWx0ZXI6IGFueSA9IHt9XG4gICAgICAgIGlmICh2ZXNzZWxJZCAmJiB2ZXNzZWxJZCA+IDApIHtcbiAgICAgICAgICAgIGR1ZXNGaWx0ZXIudmVzc2VsSUQgPSB7IGVxOiArdmVzc2VsSWQgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChtZW1iZXJJZCAmJiArbWVtYmVySWQgPiAwKSB7XG4gICAgICAgICAgICBkdWVzRmlsdGVyLm1lbWJlcnMgPSB7IGlkOiB7IGNvbnRhaW5zOiArbWVtYmVySWQgfSB9XG4gICAgICAgIH1cbiAgICAgICAgYXdhaXQgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMoZHVlc0ZpbHRlcilcblxuICAgICAgICAvLyBMb2FkIGNvbXBsZXRlZCB0cmFpbmluZyB3aXRob3V0IGZpbHRlcnMgKGNsaWVudC1zaWRlIGZpbHRlcmluZyB3aWxsIGhhbmRsZSB0aGlzKVxuICAgICAgICBjb25zdCBjb21wbGV0ZWRGaWx0ZXI6IGFueSA9IHt9XG4gICAgICAgIGlmICh2ZXNzZWxJZCAmJiB2ZXNzZWxJZCA+IDApIHtcbiAgICAgICAgICAgIGNvbXBsZXRlZEZpbHRlci52ZXNzZWxJRCA9IHsgZXE6ICt2ZXNzZWxJZCB9XG4gICAgICAgIH1cbiAgICAgICAgYXdhaXQgbG9hZFRyYWluaW5nTGlzdCgwLCBjb21wbGV0ZWRGaWx0ZXIpXG5cbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH0sIFt2ZXNzZWxJZCwgbWVtYmVySWQsIGxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzLCBsb2FkVHJhaW5pbmdMaXN0XSlcblxuICAgIC8vIExvYWQgZGF0YSBvbiBjb21wb25lbnQgbW91bnRcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBsb2FkRGF0YSgpXG4gICAgfSwgW2xvYWREYXRhXSlcblxuICAgIC8vIEVuaGFuY2VkIGxvYWRpbmcgc3RhdGUgbWFuYWdlbWVudFxuICAgIGNvbnN0IGhhc092ZXJkdWVVcGNvbWluZ0RhdGEgPVxuICAgICAgICB0cmFpbmluZ1Nlc3Npb25EdWVzICYmIHRyYWluaW5nU2Vzc2lvbkR1ZXMubGVuZ3RoID4gMFxuICAgIGNvbnN0IGhhc0NvbXBsZXRlZERhdGEgPVxuICAgICAgICBjb21wbGV0ZWRUcmFpbmluZ0xpc3QgJiYgY29tcGxldGVkVHJhaW5pbmdMaXN0Lmxlbmd0aCA+IDBcblxuICAgIC8vIENyZWF0ZSBkZXRhaWxlZCBsb2FkaW5nIHN0YXR1c1xuICAgIGNvbnN0IGdldExvYWRpbmdTdGF0dXMgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHN0YXR1c2VzID0gW11cbiAgICAgICAgaWYgKGR1ZXNMb2FkaW5nKSBzdGF0dXNlcy5wdXNoKCdvdmVyZHVlL3VwY29taW5nIHRyYWluaW5nJylcbiAgICAgICAgaWYgKGNvbXBsZXRlZExvYWRpbmcpIHN0YXR1c2VzLnB1c2goJ2NvbXBsZXRlZCB0cmFpbmluZycpXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHN0YXR1c2VzLnB1c2goJ3RyYWluaW5nIGRhdGEnKVxuICAgICAgICByZXR1cm4gc3RhdHVzZXNcbiAgICB9XG5cbiAgICAvLyBDb21wcmVoZW5zaXZlIGxvYWRpbmcgY29tcG9uZW50XG4gICAgY29uc3QgTG9hZGluZ0luZGljYXRvciA9ICh7IHN0YXR1cyB9OiB7IHN0YXR1czogc3RyaW5nW10gfSkgPT4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC04IHctOCBhbmltYXRlLXNwaW4gbXgtYXV0byB0ZXh0LXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIExvYWRpbmcgdHJhaW5pbmcgZGF0YS4uLlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0dXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEN1cnJlbnRseSBsb2FkaW5nOiB7c3RhdHVzLmpvaW4oJywgJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgKVxuXG4gICAgLy8gUGFydGlhbCBsb2FkaW5nIGNvbXBvbmVudCBmb3Igd2hlbiBzb21lIGRhdGEgaXMgYXZhaWxhYmxlXG4gICAgY29uc3QgUGFydGlhbExvYWRpbmdJbmRpY2F0b3IgPSAoeyBzdGF0dXMgfTogeyBzdGF0dXM6IHN0cmluZ1tdIH0pID0+IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS00IGJnLW11dGVkLzMwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+TG9hZGluZyB7c3RhdHVzLmpvaW4oJywgJyl9Li4uPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgIClcblxuICAgIC8vIENoZWNrIHBlcm1pc3Npb25zXG4gICAgaWYgKFxuICAgICAgICAhcGVybWlzc2lvbnMgfHxcbiAgICAgICAgKCFoYXNQZXJtaXNzaW9uKCdFRElUX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpICYmXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignVklFV19UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1JFQ09SRF9UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1ZJRVdfTUVNQkVSX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpKVxuICAgICkge1xuICAgICAgICByZXR1cm4gIXBlcm1pc3Npb25zID8gKFxuICAgICAgICAgICAgPExvYWRpbmcgLz5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxMb2FkaW5nIGVycm9yTWVzc2FnZT1cIk9vcHMgWW91IGRvIG5vdCBoYXZlIHRoZSBwZXJtaXNzaW9uIHRvIHZpZXcgdGhpcyBzZWN0aW9uLlwiIC8+XG4gICAgICAgIClcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBGaWx0ZXIgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxVbmlmaWVkVHJhaW5pbmdGaWx0ZXJcbiAgICAgICAgICAgICAgICAgICAgbWVtYmVySWQ9e21lbWJlcklkfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlRmlsdGVyQ2hhbmdlfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBIZWFkZXIgd2l0aCBzdGF0aXN0aWNzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlbiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SDI+Q3JldyBUcmFpbmluZyBPdmVydmlldzwvSDI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBVbmlmaWVkIHZpZXcgb2YgYWxsIHRyYWluaW5nIGFjdGl2aXRpZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBVbmlmaWVkIHRyYWluaW5nIHRhYmxlIHdpdGggZW5oYW5jZWQgbG9hZGluZyBzdGF0ZXMgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAhaGFzT3ZlcmR1ZVVwY29taW5nRGF0YSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgIWhhc0NvbXBsZXRlZERhdGEgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRnVsbCBsb2FkaW5nIHN0YXRlIHdoZW4gbm8gZGF0YSBpcyBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TG9hZGluZ0luZGljYXRvciBzdGF0dXM9e2dldExvYWRpbmdTdGF0dXMoKX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiBpc0xvYWRpbmcgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKGhhc092ZXJkdWVVcGNvbWluZ0RhdGEgfHwgaGFzQ29tcGxldGVkRGF0YSkgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gUGFydGlhbCBsb2FkaW5nIHN0YXRlIHdoZW4gc29tZSBkYXRhIGlzIGF2YWlsYWJsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxVbmlmaWVkVHJhaW5pbmdUYWJsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdW5pZmllZERhdGE9e2ZpbHRlcmVkRGF0YX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldFZlc3NlbFdpdGhJY29uPXtnZXRWZXNzZWxXaXRoSWNvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2x1ZGVDb21wbGV0ZWQ9e2luY2x1ZGVDb21wbGV0ZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJZD17bWVtYmVySWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1Zlc3NlbFZpZXc9e2lzVmVzc2VsVmlld31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsyMH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBhcnRpYWxMb2FkaW5nSW5kaWNhdG9yXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM9e2dldExvYWRpbmdTdGF0dXMoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gTm9ybWFsIHN0YXRlIHdpdGggZGF0YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxVbmlmaWVkVHJhaW5pbmdUYWJsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1bmlmaWVkRGF0YT17ZmlsdGVyZWREYXRhfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRWZXNzZWxXaXRoSWNvbj17Z2V0VmVzc2VsV2l0aEljb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluY2x1ZGVDb21wbGV0ZWQ9e2luY2x1ZGVDb21wbGV0ZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNWZXNzZWxWaWV3PXtpc1Zlc3NlbFZpZXd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9ezIwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogTG9hZGluZyBzdGF0dXMgaW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIChoYXNPdmVyZHVlVXBjb21pbmdEYXRhIHx8IGhhc0NvbXBsZXRlZERhdGEpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU29tZSBkYXRhIGlzIHN0aWxsIGxvYWRpbmcuIFRoZSB0YWJsZSB3aWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGUgYXV0b21hdGljYWxseSB3aGVuIG5ldyBkYXRhIGJlY29tZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF2YWlsYWJsZS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogTm8gZGF0YSBzdGF0ZSAqL31cbiAgICAgICAgICAgICAgICAgICAgeyFpc0xvYWRpbmcgJiYgdW5pZmllZERhdGEubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vIHRyYWluaW5nIGRhdGEgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUcnkgYWRqdXN0aW5nIHlvdXIgZmlsdGVycyBvciByZWZyZXNoIHRoZSBkYXRhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuICAgIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgVW5pZmllZFRyYWluaW5nRXhhbXBsZVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZUNhbGxiYWNrIiwidXNlTGF6eVF1ZXJ5IiwiVFJBSU5JTkdfU0VTU0lPTlMiLCJSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUyIsIlVuaWZpZWRUcmFpbmluZ1RhYmxlIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJDYXJkIiwiSDIiLCJtZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhIiwiVW5pZmllZFRyYWluaW5nRmlsdGVyIiwidXNlVW5pZmllZFRyYWluaW5nRmlsdGVycyIsImdldFBlcm1pc3Npb25zIiwiaGFzUGVybWlzc2lvbiIsIkxvYWRpbmciLCJMb2FkZXIyIiwiVW5pZmllZFRyYWluaW5nRXhhbXBsZSIsInZlc3NlbElkIiwibWVtYmVySWQiLCJpc1Zlc3NlbFZpZXciLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0cmFpbmluZ1Nlc3Npb25EdWVzIiwic2V0VHJhaW5pbmdTZXNzaW9uRHVlcyIsImNvbXBsZXRlZFRyYWluaW5nTGlzdCIsInNldENvbXBsZXRlZFRyYWluaW5nTGlzdCIsImluY2x1ZGVDb21wbGV0ZWQiLCJwZXJtaXNzaW9ucyIsInNldFBlcm1pc3Npb25zIiwiZ2V0VmVzc2VsV2l0aEljb24iLCJxdWVyeVRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJsb2FkaW5nIiwiZHVlc0xvYWRpbmciLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJkYXRhIiwicmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJub2RlcyIsImNvbnNvbGUiLCJsb2ciLCJ0b3RhbFJlY29yZHMiLCJsZW5ndGgiLCJzYW1wbGVSZWNvcmQiLCJhbGxSZWNvcmRzIiwib25FcnJvciIsImVycm9yIiwicXVlcnlDb21wbGV0ZWRUcmFpbmluZyIsImNvbXBsZXRlZExvYWRpbmciLCJyZWFkVHJhaW5pbmdTZXNzaW9ucyIsImxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwiZmlsdGVyIiwiZHVlc0ZpbHRlciIsIm1lbWJlcklEIiwiZXEiLCJ2ZXNzZWxJRCIsInRyYWluaW5nVHlwZXMiLCJ0cmFpbmluZ1R5cGVJRCIsImlkIiwiY29udGFpbnMiLCJtZW1iZXJzIiwiZGF0ZSIsImR1ZURhdGUiLCJuZSIsInZhcmlhYmxlcyIsImxvYWRUcmFpbmluZ0xpc3QiLCJzdGFydFBhZ2UiLCJzZWFyY2hGaWx0ZXIiLCJjb21wbGV0ZWRGaWx0ZXIiLCJvZmZzZXQiLCJsaW1pdCIsInVuaWZpZWREYXRhIiwic3RhcnRUaW1lIiwicGVyZm9ybWFuY2UiLCJub3ciLCJ0cmFpbmluZ1Nlc3Npb25EdWVzQ291bnQiLCJjb21wbGV0ZWRUcmFpbmluZ0xpc3RDb3VudCIsInNhbXBsZUR1ZSIsInNhbXBsZUNvbXBsZXRlZCIsInJlc3VsdCIsImVuZFRpbWUiLCJtZXJnZVRpbWUiLCJ0b0ZpeGVkIiwiY2F0ZWdvcnlCcmVha2Rvd24iLCJyZWR1Y2UiLCJhY2MiLCJpdGVtIiwiY2F0ZWdvcnkiLCJoYW5kbGVGaWx0ZXJDaGFuZ2UiLCJmaWx0ZXJlZERhdGEiLCJpbml0aWFsRmlsdGVyIiwibG9hZERhdGEiLCJoYXNPdmVyZHVlVXBjb21pbmdEYXRhIiwiaGFzQ29tcGxldGVkRGF0YSIsImdldExvYWRpbmdTdGF0dXMiLCJzdGF0dXNlcyIsInB1c2giLCJMb2FkaW5nSW5kaWNhdG9yIiwic3RhdHVzIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImpvaW4iLCJQYXJ0aWFsTG9hZGluZ0luZGljYXRvciIsInNwYW4iLCJlcnJvck1lc3NhZ2UiLCJvbkNoYW5nZSIsInNob3dUb29sYmFyIiwicGFnZVNpemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});