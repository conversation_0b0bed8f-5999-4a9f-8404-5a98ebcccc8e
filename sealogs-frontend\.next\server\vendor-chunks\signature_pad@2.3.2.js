"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/signature_pad@2.3.2";
exports.ids = ["vendor-chunks/signature_pad@2.3.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/signature_pad@2.3.2/node_modules/signature_pad/dist/signature_pad.mjs":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/signature_pad@2.3.2/node_modules/signature_pad/dist/signature_pad.mjs ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*!\n * Signature Pad v2.3.2\n * https://github.com/szimek/signature_pad\n *\n * Copyright 2017 Szymon Nowak\n * Released under the MIT license\n *\n * The main idea and some parts of the code (e.g. drawing variable width Bézier curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://benknowscode.wordpress.com/2012/09/14/path-interpolation-using-cubic-bezier-and-control-point-estimation-in-javascript\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n *\n */\n\nfunction Point(x, y, time) {\n  this.x = x;\n  this.y = y;\n  this.time = time || new Date().getTime();\n}\n\nPoint.prototype.velocityFrom = function (start) {\n  return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 1;\n};\n\nPoint.prototype.distanceTo = function (start) {\n  return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n};\n\nPoint.prototype.equals = function (other) {\n  return this.x === other.x && this.y === other.y && this.time === other.time;\n};\n\nfunction Bezier(startPoint, control1, control2, endPoint) {\n  this.startPoint = startPoint;\n  this.control1 = control1;\n  this.control2 = control2;\n  this.endPoint = endPoint;\n}\n\n// Returns approximated length.\nBezier.prototype.length = function () {\n  var steps = 10;\n  var length = 0;\n  var px = void 0;\n  var py = void 0;\n\n  for (var i = 0; i <= steps; i += 1) {\n    var t = i / steps;\n    var cx = this._point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n    var cy = this._point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n    if (i > 0) {\n      var xdiff = cx - px;\n      var ydiff = cy - py;\n      length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n    }\n    px = cx;\n    py = cy;\n  }\n\n  return length;\n};\n\n/* eslint-disable no-multi-spaces, space-in-parens */\nBezier.prototype._point = function (t, start, c1, c2, end) {\n  return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;\n};\n\n/* eslint-disable */\n\n// http://stackoverflow.com/a/27078401/815507\nfunction throttle(func, wait, options) {\n  var context, args, result;\n  var timeout = null;\n  var previous = 0;\n  if (!options) options = {};\n  var later = function later() {\n    previous = options.leading === false ? 0 : Date.now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n  return function () {\n    var now = Date.now();\n    if (!previous && options.leading === false) previous = now;\n    var remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\n\nfunction SignaturePad(canvas, options) {\n  var self = this;\n  var opts = options || {};\n\n  this.velocityFilterWeight = opts.velocityFilterWeight || 0.7;\n  this.minWidth = opts.minWidth || 0.5;\n  this.maxWidth = opts.maxWidth || 2.5;\n  this.throttle = 'throttle' in opts ? opts.throttle : 16; // in miliseconds\n  this.minDistance = 'minDistance' in opts ? opts.minDistance : 5;\n\n  if (this.throttle) {\n    this._strokeMoveUpdate = throttle(SignaturePad.prototype._strokeUpdate, this.throttle);\n  } else {\n    this._strokeMoveUpdate = SignaturePad.prototype._strokeUpdate;\n  }\n\n  this.dotSize = opts.dotSize || function () {\n    return (this.minWidth + this.maxWidth) / 2;\n  };\n  this.penColor = opts.penColor || 'black';\n  this.backgroundColor = opts.backgroundColor || 'rgba(0,0,0,0)';\n  this.onBegin = opts.onBegin;\n  this.onEnd = opts.onEnd;\n\n  this._canvas = canvas;\n  this._ctx = canvas.getContext('2d');\n  this.clear();\n\n  // We need add these inline so they are available to unbind while still having\n  // access to 'self' we could use _.bind but it's not worth adding a dependency.\n  this._handleMouseDown = function (event) {\n    if (event.which === 1) {\n      self._mouseButtonDown = true;\n      self._strokeBegin(event);\n    }\n  };\n\n  this._handleMouseMove = function (event) {\n    if (self._mouseButtonDown) {\n      self._strokeMoveUpdate(event);\n    }\n  };\n\n  this._handleMouseUp = function (event) {\n    if (event.which === 1 && self._mouseButtonDown) {\n      self._mouseButtonDown = false;\n      self._strokeEnd(event);\n    }\n  };\n\n  this._handleTouchStart = function (event) {\n    if (event.targetTouches.length === 1) {\n      var touch = event.changedTouches[0];\n      self._strokeBegin(touch);\n    }\n  };\n\n  this._handleTouchMove = function (event) {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    var touch = event.targetTouches[0];\n    self._strokeMoveUpdate(touch);\n  };\n\n  this._handleTouchEnd = function (event) {\n    var wasCanvasTouched = event.target === self._canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n      self._strokeEnd(event);\n    }\n  };\n\n  // Enable mouse and touch event handlers\n  this.on();\n}\n\n// Public methods\nSignaturePad.prototype.clear = function () {\n  var ctx = this._ctx;\n  var canvas = this._canvas;\n\n  ctx.fillStyle = this.backgroundColor;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n  this._data = [];\n  this._reset();\n  this._isEmpty = true;\n};\n\nSignaturePad.prototype.fromDataURL = function (dataUrl) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var image = new Image();\n  var ratio = options.ratio || window.devicePixelRatio || 1;\n  var width = options.width || this._canvas.width / ratio;\n  var height = options.height || this._canvas.height / ratio;\n\n  this._reset();\n  image.src = dataUrl;\n  image.onload = function () {\n    _this._ctx.drawImage(image, 0, 0, width, height);\n  };\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype.toDataURL = function (type) {\n  var _canvas;\n\n  switch (type) {\n    case 'image/svg+xml':\n      return this._toSVG();\n    default:\n      for (var _len = arguments.length, options = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        options[_key - 1] = arguments[_key];\n      }\n\n      return (_canvas = this._canvas).toDataURL.apply(_canvas, [type].concat(options));\n  }\n};\n\nSignaturePad.prototype.on = function () {\n  this._handleMouseEvents();\n  this._handleTouchEvents();\n};\n\nSignaturePad.prototype.off = function () {\n  this._canvas.removeEventListener('mousedown', this._handleMouseDown);\n  this._canvas.removeEventListener('mousemove', this._handleMouseMove);\n  document.removeEventListener('mouseup', this._handleMouseUp);\n\n  this._canvas.removeEventListener('touchstart', this._handleTouchStart);\n  this._canvas.removeEventListener('touchmove', this._handleTouchMove);\n  this._canvas.removeEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype.isEmpty = function () {\n  return this._isEmpty;\n};\n\n// Private methods\nSignaturePad.prototype._strokeBegin = function (event) {\n  this._data.push([]);\n  this._reset();\n  this._strokeUpdate(event);\n\n  if (typeof this.onBegin === 'function') {\n    this.onBegin(event);\n  }\n};\n\nSignaturePad.prototype._strokeUpdate = function (event) {\n  var x = event.clientX;\n  var y = event.clientY;\n\n  var point = this._createPoint(x, y);\n  var lastPointGroup = this._data[this._data.length - 1];\n  var lastPoint = lastPointGroup && lastPointGroup[lastPointGroup.length - 1];\n  var isLastPointTooClose = lastPoint && point.distanceTo(lastPoint) < this.minDistance;\n\n  // Skip this point if it's too close to the previous one\n  if (!(lastPoint && isLastPointTooClose)) {\n    var _addPoint = this._addPoint(point),\n        curve = _addPoint.curve,\n        widths = _addPoint.widths;\n\n    if (curve && widths) {\n      this._drawCurve(curve, widths.start, widths.end);\n    }\n\n    this._data[this._data.length - 1].push({\n      x: point.x,\n      y: point.y,\n      time: point.time,\n      color: this.penColor\n    });\n  }\n};\n\nSignaturePad.prototype._strokeEnd = function (event) {\n  var canDrawCurve = this.points.length > 2;\n  var point = this.points[0]; // Point instance\n\n  if (!canDrawCurve && point) {\n    this._drawDot(point);\n  }\n\n  if (point) {\n    var lastPointGroup = this._data[this._data.length - 1];\n    var lastPoint = lastPointGroup[lastPointGroup.length - 1]; // plain object\n\n    // When drawing a dot, there's only one point in a group, so without this check\n    // such group would end up with exactly the same 2 points.\n    if (!point.equals(lastPoint)) {\n      lastPointGroup.push({\n        x: point.x,\n        y: point.y,\n        time: point.time,\n        color: this.penColor\n      });\n    }\n  }\n\n  if (typeof this.onEnd === 'function') {\n    this.onEnd(event);\n  }\n};\n\nSignaturePad.prototype._handleMouseEvents = function () {\n  this._mouseButtonDown = false;\n\n  this._canvas.addEventListener('mousedown', this._handleMouseDown);\n  this._canvas.addEventListener('mousemove', this._handleMouseMove);\n  document.addEventListener('mouseup', this._handleMouseUp);\n};\n\nSignaturePad.prototype._handleTouchEvents = function () {\n  // Pass touch events to canvas element on mobile IE11 and Edge.\n  this._canvas.style.msTouchAction = 'none';\n  this._canvas.style.touchAction = 'none';\n\n  this._canvas.addEventListener('touchstart', this._handleTouchStart);\n  this._canvas.addEventListener('touchmove', this._handleTouchMove);\n  this._canvas.addEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype._reset = function () {\n  this.points = [];\n  this._lastVelocity = 0;\n  this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n  this._ctx.fillStyle = this.penColor;\n};\n\nSignaturePad.prototype._createPoint = function (x, y, time) {\n  var rect = this._canvas.getBoundingClientRect();\n\n  return new Point(x - rect.left, y - rect.top, time || new Date().getTime());\n};\n\nSignaturePad.prototype._addPoint = function (point) {\n  var points = this.points;\n  var tmp = void 0;\n\n  points.push(point);\n\n  if (points.length > 2) {\n    // To reduce the initial lag make it work with 3 points\n    // by copying the first point to the beginning.\n    if (points.length === 3) points.unshift(points[0]);\n\n    tmp = this._calculateCurveControlPoints(points[0], points[1], points[2]);\n    var c2 = tmp.c2;\n    tmp = this._calculateCurveControlPoints(points[1], points[2], points[3]);\n    var c3 = tmp.c1;\n    var curve = new Bezier(points[1], c2, c3, points[2]);\n    var widths = this._calculateCurveWidths(curve);\n\n    // Remove the first element from the list,\n    // so that we always have no more than 4 points in points array.\n    points.shift();\n\n    return { curve: curve, widths: widths };\n  }\n\n  return {};\n};\n\nSignaturePad.prototype._calculateCurveControlPoints = function (s1, s2, s3) {\n  var dx1 = s1.x - s2.x;\n  var dy1 = s1.y - s2.y;\n  var dx2 = s2.x - s3.x;\n  var dy2 = s2.y - s3.y;\n\n  var m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n  var m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n  var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n  var dxm = m1.x - m2.x;\n  var dym = m1.y - m2.y;\n\n  var k = l2 / (l1 + l2);\n  var cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n  var tx = s2.x - cm.x;\n  var ty = s2.y - cm.y;\n\n  return {\n    c1: new Point(m1.x + tx, m1.y + ty),\n    c2: new Point(m2.x + tx, m2.y + ty)\n  };\n};\n\nSignaturePad.prototype._calculateCurveWidths = function (curve) {\n  var startPoint = curve.startPoint;\n  var endPoint = curve.endPoint;\n  var widths = { start: null, end: null };\n\n  var velocity = this.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - this.velocityFilterWeight) * this._lastVelocity;\n\n  var newWidth = this._strokeWidth(velocity);\n\n  widths.start = this._lastWidth;\n  widths.end = newWidth;\n\n  this._lastVelocity = velocity;\n  this._lastWidth = newWidth;\n\n  return widths;\n};\n\nSignaturePad.prototype._strokeWidth = function (velocity) {\n  return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n};\n\nSignaturePad.prototype._drawPoint = function (x, y, size) {\n  var ctx = this._ctx;\n\n  ctx.moveTo(x, y);\n  ctx.arc(x, y, size, 0, 2 * Math.PI, false);\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype._drawCurve = function (curve, startWidth, endWidth) {\n  var ctx = this._ctx;\n  var widthDelta = endWidth - startWidth;\n  var drawSteps = Math.floor(curve.length());\n\n  ctx.beginPath();\n\n  for (var i = 0; i < drawSteps; i += 1) {\n    // Calculate the Bezier (x, y) coordinate for this step.\n    var t = i / drawSteps;\n    var tt = t * t;\n    var ttt = tt * t;\n    var u = 1 - t;\n    var uu = u * u;\n    var uuu = uu * u;\n\n    var x = uuu * curve.startPoint.x;\n    x += 3 * uu * t * curve.control1.x;\n    x += 3 * u * tt * curve.control2.x;\n    x += ttt * curve.endPoint.x;\n\n    var y = uuu * curve.startPoint.y;\n    y += 3 * uu * t * curve.control1.y;\n    y += 3 * u * tt * curve.control2.y;\n    y += ttt * curve.endPoint.y;\n\n    var width = startWidth + ttt * widthDelta;\n    this._drawPoint(x, y, width);\n  }\n\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._drawDot = function (point) {\n  var ctx = this._ctx;\n  var width = typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n\n  ctx.beginPath();\n  this._drawPoint(point.x, point.y, width);\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._fromData = function (pointGroups, drawCurve, drawDot) {\n  for (var i = 0; i < pointGroups.length; i += 1) {\n    var group = pointGroups[i];\n\n    if (group.length > 1) {\n      for (var j = 0; j < group.length; j += 1) {\n        var rawPoint = group[j];\n        var point = new Point(rawPoint.x, rawPoint.y, rawPoint.time);\n        var color = rawPoint.color;\n\n        if (j === 0) {\n          // First point in a group. Nothing to draw yet.\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n          this._reset();\n\n          this._addPoint(point);\n        } else if (j !== group.length - 1) {\n          // Middle point in a group.\n          var _addPoint2 = this._addPoint(point),\n              curve = _addPoint2.curve,\n              widths = _addPoint2.widths;\n\n          if (curve && widths) {\n            drawCurve(curve, widths, color);\n          }\n        } else {\n          // Last point in a group. Do nothing.\n        }\n      }\n    } else {\n      this._reset();\n      var _rawPoint = group[0];\n      drawDot(_rawPoint);\n    }\n  }\n};\n\nSignaturePad.prototype._toSVG = function () {\n  var _this2 = this;\n\n  var pointGroups = this._data;\n  var canvas = this._canvas;\n  var ratio = Math.max(window.devicePixelRatio || 1, 1);\n  var minX = 0;\n  var minY = 0;\n  var maxX = canvas.width / ratio;\n  var maxY = canvas.height / ratio;\n  var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n  svg.setAttributeNS(null, 'width', canvas.width);\n  svg.setAttributeNS(null, 'height', canvas.height);\n\n  this._fromData(pointGroups, function (curve, widths, color) {\n    var path = document.createElement('path');\n\n    // Need to check curve for NaN values, these pop up when drawing\n    // lines on the canvas that are not continuous. E.g. Sharp corners\n    // or stopping mid-stroke and than continuing without lifting mouse.\n    if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {\n      var attr = 'M ' + curve.startPoint.x.toFixed(3) + ',' + curve.startPoint.y.toFixed(3) + ' ' + ('C ' + curve.control1.x.toFixed(3) + ',' + curve.control1.y.toFixed(3) + ' ') + (curve.control2.x.toFixed(3) + ',' + curve.control2.y.toFixed(3) + ' ') + (curve.endPoint.x.toFixed(3) + ',' + curve.endPoint.y.toFixed(3));\n\n      path.setAttribute('d', attr);\n      path.setAttribute('stroke-width', (widths.end * 2.25).toFixed(3));\n      path.setAttribute('stroke', color);\n      path.setAttribute('fill', 'none');\n      path.setAttribute('stroke-linecap', 'round');\n\n      svg.appendChild(path);\n    }\n  }, function (rawPoint) {\n    var circle = document.createElement('circle');\n    var dotSize = typeof _this2.dotSize === 'function' ? _this2.dotSize() : _this2.dotSize;\n    circle.setAttribute('r', dotSize);\n    circle.setAttribute('cx', rawPoint.x);\n    circle.setAttribute('cy', rawPoint.y);\n    circle.setAttribute('fill', rawPoint.color);\n\n    svg.appendChild(circle);\n  });\n\n  var prefix = 'data:image/svg+xml;base64,';\n  var header = '<svg' + ' xmlns=\"http://www.w3.org/2000/svg\"' + ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' + (' viewBox=\"' + minX + ' ' + minY + ' ' + maxX + ' ' + maxY + '\"') + (' width=\"' + maxX + '\"') + (' height=\"' + maxY + '\"') + '>';\n  var body = svg.innerHTML;\n\n  // IE hack for missing innerHTML property on SVGElement\n  if (body === undefined) {\n    var dummy = document.createElement('dummy');\n    var nodes = svg.childNodes;\n    dummy.innerHTML = '';\n\n    for (var i = 0; i < nodes.length; i += 1) {\n      dummy.appendChild(nodes[i].cloneNode(true));\n    }\n\n    body = dummy.innerHTML;\n  }\n\n  var footer = '</svg>';\n  var data = header + body + footer;\n\n  return prefix + btoa(data);\n};\n\nSignaturePad.prototype.fromData = function (pointGroups) {\n  var _this3 = this;\n\n  this.clear();\n\n  this._fromData(pointGroups, function (curve, widths) {\n    return _this3._drawCurve(curve, widths.start, widths.end);\n  }, function (rawPoint) {\n    return _this3._drawDot(rawPoint);\n  });\n\n  this._data = pointGroups;\n};\n\nSignaturePad.prototype.toData = function () {\n  return this._data;\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SignaturePad);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/signature_pad@2.3.2/node_modules/signature_pad/dist/signature_pad.mjs\n");

/***/ })

};
;