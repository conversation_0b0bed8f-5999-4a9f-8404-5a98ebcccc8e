"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainingTypes = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainingTypes = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data provided for filtering\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(filter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering unified data:\", {\n            totalRecords: unifiedData.length,\n            activeFilters: Object.keys(filter).filter((key)=>filter[key] !== undefined),\n            filterValues: filter,\n            cacheKey: cacheKey.substring(0, 100) + \"...\" // Truncate for logging\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (filter.category && filter.category !== \"all\") {\n                if (item.category !== filter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (filter.vesselID) {\n                if (filter.vesselID.eq && item.vesselID !== filter.vesselID.eq) {\n                    return false;\n                }\n                if (filter.vesselID.in && !filter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (filter.trainingTypes) {\n                var _item_trainingType, _filter_trainingTypes_id, _filter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                if (((_filter_trainingTypes_id = filter.trainingTypes.id) === null || _filter_trainingTypes_id === void 0 ? void 0 : _filter_trainingTypes_id.contains) && trainingTypeId !== filter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_filter_trainingTypes_id1 = filter.trainingTypes.id) === null || _filter_trainingTypes_id1 === void 0 ? void 0 : _filter_trainingTypes_id1.in) && !filter.trainingTypes.id.in.includes(trainingTypeId)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (filter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _filter_trainer_id, _filter_trainer_id1;\n                    if (((_filter_trainer_id = filter.trainer.id) === null || _filter_trainer_id === void 0 ? void 0 : _filter_trainer_id.eq) && trainerId !== filter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_filter_trainer_id1 = filter.trainer.id) === null || _filter_trainer_id1 === void 0 ? void 0 : _filter_trainer_id1.in) && !filter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (filter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (filter.members && item.members) {\n                var _filter_members_id, _filter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_filter_members_id = filter.members.id) === null || _filter_members_id === void 0 ? void 0 : _filter_members_id.eq) && !memberIds.includes(filter.members.id.eq)) {\n                    return false;\n                }\n                if ((_filter_members_id1 = filter.members.id) === null || _filter_members_id1 === void 0 ? void 0 : _filter_members_id1.in) {\n                    const hasMatchingMember = filter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (filter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (filter.date.gte && itemDate < filter.date.gte) {\n                    return false;\n                }\n                if (filter.date.lte && itemDate > filter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            categoryBreakdown: filtered.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            filterCache.delete(firstKey);\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        filter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});