"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filter/unified-training-filter */ \"(app-pages-browser)/./src/components/filter/unified-training-filter.tsx\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const includeCompleted = true // Always include completed in unified view\n    ;\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Simplified data loading without server-side filtering\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        // Load training session dues without filters (client-side filtering will handle this)\n        const duesFilter = {};\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (memberId && +memberId > 0) {\n            duesFilter.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        await loadTrainingSessionDues(duesFilter);\n        // Load completed training without filters (client-side filtering will handle this)\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        await loadTrainingList(0, completedFilter);\n        setIsLoading(false);\n    }, [\n        vesselId,\n        memberId,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Enhanced loading state management\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 247,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 246,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 266,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 265,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 282,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 284,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__.UnifiedTrainingFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 291,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                        children: \"Crew Training Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"Unified view of all training activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        unifiedData: filteredData,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                unifiedData: filteredData,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 21\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 298,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 289,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"5GdiogSM/IrhyxZ75aAQBzudabo=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});