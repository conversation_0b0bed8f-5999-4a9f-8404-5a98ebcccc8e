"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filter/unified-training-filter */ \"(app-pages-browser)/./src/components/filter/unified-training-filter.tsx\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Always include completed in unified view\n    ;\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw training session dues data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Raw completed training sessions data:\", {\n                totalRecords: data.length,\n                sampleRecord: data[0],\n                allRecords: data\n            });\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Input data for mergeAndSortCrewTrainingData:\", {\n            trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n            completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n            includeCompleted,\n            sampleDue: trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues[0],\n            sampleCompleted: completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList[0]\n        });\n        const result = (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n        console.log(\"\\uD83D\\uDD0D [UnifiedTrainingExample] Result from mergeAndSortCrewTrainingData:\", {\n            totalRecords: result.length,\n            categoryBreakdown: result.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: result[0],\n            allRecords: result\n        });\n        return result;\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { filter, setFilter, handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Memoize the load data function to prevent unnecessary re-renders\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        await loadTrainingSessionDues(filter);\n        await loadTrainingList(page, filter);\n        setIsLoading(false);\n    }, [\n        filter,\n        page,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const f = {\n            ...filter\n        };\n        if (memberId && +memberId > 0) {\n            f.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        setFilter(f);\n        loadData();\n    }, []) // Only run on mount\n    ;\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.getTrainingDataStats)(filteredData);\n    }, [\n        filteredData\n    ]);\n    // Enhanced loading state management\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 252,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 251,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 271,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 270,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 287,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 289,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__.UnifiedTrainingFilter, {\n                    memberId: memberId,\n                    onChange: handleFilterChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 296,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                        children: \"Crew Training Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"Unified view of all training activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        trainingSessionDues: trainingSessionDues,\n                                        completedTrainingList: completedTrainingList,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                trainingSessionDues: trainingSessionDues,\n                                completedTrainingList: completedTrainingList,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 303,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 294,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"QKq5xo37/EgNwlm8c1l9ekO8sxU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});