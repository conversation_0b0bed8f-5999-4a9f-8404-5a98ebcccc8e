"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainingTypes = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainingTypes = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data provided for filtering\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(filter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering unified data:\", {\n            totalRecords: unifiedData.length,\n            activeFilters: Object.keys(filter).filter((key)=>filter[key] !== undefined),\n            filterValues: filter,\n            cacheKey: cacheKey.substring(0, 100) + \"...\" // Truncate for logging\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (filter.category && filter.category !== \"all\") {\n                if (item.category !== filter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (filter.vesselID) {\n                if (filter.vesselID.eq && item.vesselID !== filter.vesselID.eq) {\n                    return false;\n                }\n                if (filter.vesselID.in && !filter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (filter.trainingTypes) {\n                var _item_trainingType, _filter_trainingTypes_id, _filter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                if (((_filter_trainingTypes_id = filter.trainingTypes.id) === null || _filter_trainingTypes_id === void 0 ? void 0 : _filter_trainingTypes_id.contains) && trainingTypeId !== filter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_filter_trainingTypes_id1 = filter.trainingTypes.id) === null || _filter_trainingTypes_id1 === void 0 ? void 0 : _filter_trainingTypes_id1.in) && !filter.trainingTypes.id.in.includes(trainingTypeId)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (filter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _filter_trainer_id, _filter_trainer_id1;\n                    if (((_filter_trainer_id = filter.trainer.id) === null || _filter_trainer_id === void 0 ? void 0 : _filter_trainer_id.eq) && trainerId !== filter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_filter_trainer_id1 = filter.trainer.id) === null || _filter_trainer_id1 === void 0 ? void 0 : _filter_trainer_id1.in) && !filter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (filter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (filter.members && item.members) {\n                var _filter_members_id, _filter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_filter_members_id = filter.members.id) === null || _filter_members_id === void 0 ? void 0 : _filter_members_id.eq) && !memberIds.includes(filter.members.id.eq)) {\n                    return false;\n                }\n                if ((_filter_members_id1 = filter.members.id) === null || _filter_members_id1 === void 0 ? void 0 : _filter_members_id1.in) {\n                    const hasMatchingMember = filter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (filter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (filter.date.gte && itemDate < filter.date.gte) {\n                    return false;\n                }\n                if (filter.date.lte && itemDate > filter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            categoryBreakdown: filtered.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        filter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});