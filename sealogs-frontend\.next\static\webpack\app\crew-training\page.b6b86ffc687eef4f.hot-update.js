"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainingTypes = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainingTypes = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data provided for filtering\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering unified data:\", {\n            totalRecords: unifiedData.length,\n            activeFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key] !== undefined),\n            filterValues: debouncedFilter,\n            cacheKey: cacheKey.substring(0, 100) + \"...\" // Truncate for logging\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (filter.category && filter.category !== \"all\") {\n                if (item.category !== filter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (filter.vesselID) {\n                if (filter.vesselID.eq && item.vesselID !== filter.vesselID.eq) {\n                    return false;\n                }\n                if (filter.vesselID.in && !filter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (filter.trainingTypes) {\n                var _item_trainingType, _filter_trainingTypes_id, _filter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                if (((_filter_trainingTypes_id = filter.trainingTypes.id) === null || _filter_trainingTypes_id === void 0 ? void 0 : _filter_trainingTypes_id.contains) && trainingTypeId !== filter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_filter_trainingTypes_id1 = filter.trainingTypes.id) === null || _filter_trainingTypes_id1 === void 0 ? void 0 : _filter_trainingTypes_id1.in) && !filter.trainingTypes.id.in.includes(trainingTypeId)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (filter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _filter_trainer_id, _filter_trainer_id1;\n                    if (((_filter_trainer_id = filter.trainer.id) === null || _filter_trainer_id === void 0 ? void 0 : _filter_trainer_id.eq) && trainerId !== filter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_filter_trainer_id1 = filter.trainer.id) === null || _filter_trainer_id1 === void 0 ? void 0 : _filter_trainer_id1.in) && !filter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (filter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (filter.members && item.members) {\n                var _filter_members_id, _filter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_filter_members_id = filter.members.id) === null || _filter_members_id === void 0 ? void 0 : _filter_members_id.eq) && !memberIds.includes(filter.members.id.eq)) {\n                    return false;\n                }\n                if ((_filter_members_id1 = filter.members.id) === null || _filter_members_id1 === void 0 ? void 0 : _filter_members_id1.in) {\n                    const hasMatchingMember = filter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (filter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (filter.date.gte && itemDate < filter.date.gte) {\n                    return false;\n                }\n                if (filter.date.lte && itemDate > filter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            categoryBreakdown: filtered.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        filter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});