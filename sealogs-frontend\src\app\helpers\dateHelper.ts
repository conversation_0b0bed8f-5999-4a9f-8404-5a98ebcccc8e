import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'

/**
 * Formats a given date string or Date object into a string in the format of
 * DD/MM/YY or DD/MM/YYYY, depending on the twoDigitYear parameter.
 * If the date string is in 'YYYY-MM-DD' format, it converts it to
 * 'DD/MM/YY' or 'DD/MM/YYYY' format without timezone conversion.
 * For other formats, it uses dayjs to parse and format.
 *
 * @param dateString - The date input, which can be a string, Date object, or any type.
 *                     Defaults to an empty string.
 * @param twoDigitYear - Boolean indicating whether to use a two-digit year format.
 *                       Defaults to true.
 * @returns A string representing the formatted date in 'DD/MM/YY' or
 *          'DD/MM/YYYY' format, or an empty string for invalid inputs.
 */

export const formatDate = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), use pure string manipulation to avoid any timezone conversion
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const [yearStr, monthStr, dayStr] = dateString.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Return in DD/MM/YY or DD/MM/YYYY format (Australian/NZ preference from memories)
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        return `${day}/${month}/${year}`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD') // DD gives day with leading zero
    const month = dayjsDate.format('MM') // MM gives month with leading zero
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')

    // Return in DD/MM/YY or DD/MM/YYYY format
    return `${day}/${month}/${year}`
}

/**
 * Formats a given date string or Date object into a string in the format of
 * DD/MM/YY HH:mm or DD/MM/YYYY HH:mm, depending on the twoDigitYear parameter.
 * If the date string is in 'YYYY-MM-DD' or 'YYYY-MM-DD HH:mm:ss' format,
 * it converts it without timezone conversion using pure string manipulation.
 * For other formats, it uses dayjs to parse and format.
 *
 * @param dateString - The date input, which can be a string, Date object, or any type.
 *                     Defaults to an empty string.
 * @param twoDigitYear - Boolean indicating whether to use a two-digit year format.
 *                       Defaults to true.
 * @returns A string representing the formatted date and time in
 *          'DD/MM/YY HH:mm' or 'DD/MM/YYYY HH:mm' format, or an empty string for
 *          invalid inputs.
 */
export const formatDateTime = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), use pure string manipulation and add default time
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const [yearStr, monthStr, dayStr] = dateString.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        // Default time to 00:00 for date-only strings
        return `${day}/${month}/${year} 00:00`
    }

    // For datetime strings (YYYY-MM-DD HH:mm:ss or YYYY-MM-DD HH:mm), use pure string manipulation to avoid timezone conversion
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(dateString)
    ) {
        const [datePart, timePart] = dateString.split(' ')
        const [yearStr, monthStr, dayStr] = datePart.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Extract time components (HH:mm or HH:mm:ss)
        const timeComponents = timePart.split(':')
        const hours = timeComponents[0].padStart(2, '0')
        const minutes = timeComponents[1].padStart(2, '0')

        // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        return `${day}/${month}/${year} ${hours}:${minutes}`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD') // DD gives day with leading zero
    const month = dayjsDate.format('MM') // MM gives month with leading zero
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')
    const time = dayjsDate.format('HH:mm') // 24-hour format with leading zeros

    // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
    return `${day}/${month}/${year} ${time}`
}

export const formatDBDateTime = (dateString: any = '') => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * Creates a new Date object from a given date string, or returns the current
 * date if no string is provided. The time is set to 10:00:00Z,
 * which is a hack to get the correct date in the local timezone.
 *
 * @param dateString - A date string in the ISO 8601 "date" format (YYYY-MM-DD)
 * @returns A Date object
 */
export const createDateOnly = (dateString: string = '') => {
    if (isEmpty(trim(dateString))) {
        return new Date()
    }
    return new Date(`${dateString}T10:00:00Z`)
}

/**
 * Checks if the actual arrival time is after the expected arrival time.
 *
 * If either argument is invalid or not a string, or if the parsed date is invalid,
 * this function will return `false` and log a warning to the console.
 *
 * The input strings can be in the following formats:
 * - A complete ISO 8601 datetime string (e.g. "2021-01-01T12:00:00Z")
 * - A date string in the ISO 8601 "date" format (YYYY-MM-DD)
 * - A time string in the format HH:mm or HH:mm:ss
 *
 * @param expectedArrival - The expected arrival time as a string
 * @param actualArrival - The actual arrival time as a string
 * @returns `true` if the actual arrival time is after the expected arrival time,
 *          `false` otherwise
 */
export const isLate = (
    expectedArrival: string | null | undefined,
    actualArrival: string | null | undefined,
): boolean => {
    const isTimeOnly = (value: string) => /^\d{1,2}:\d{2}(:\d{2})?$/.test(value)

    const normalizeDateTime = (input: string): string => {
        return input.includes(' ') ? input.replace(' ', 'T') : input
    }

    const parseInput = (input: string | null | undefined): Date | null => {
        if (!input || typeof input !== 'string') return null

        if (isTimeOnly(input)) {
            const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD
            return new Date(`${today}T${input}`)
        }

        return new Date(normalizeDateTime(input))
    }

    const expected = parseInput(expectedArrival)
    const actual = parseInput(actualArrival)

    if (
        !expected ||
        !actual ||
        isNaN(expected.getTime()) ||
        isNaN(actual.getTime())
    ) {
        console.warn('Invalid input passed to isLate():', {
            expectedArrival,
            actualArrival,
        })
        return false
    }

    return actual > expected
}
