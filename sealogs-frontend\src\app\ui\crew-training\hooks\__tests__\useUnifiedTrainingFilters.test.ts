import { renderHook, act } from '@testing-library/react'
import { useUnifiedTrainingFilters } from '../useUnifiedTrainingFilters'
import { UnifiedTrainingData } from '../../utils/crew-training-utils'

// Mock data for testing
const mockUnifiedData: UnifiedTrainingData[] = [
    {
        id: 1,
        dueDate: '2025-07-10',
        vesselID: 100,
        vessel: { id: 100, title: 'Test Vessel 1' },
        trainingTypeID: 200,
        trainingType: { id: 200, title: 'Safety Training' },
        members: [{ id: 300, firstName: '<PERSON>', surname: '<PERSON><PERSON>' }],
        status: { class: 'alert', label: 'Overdue', isOverdue: true, dueWithinSevenDays: false },
        category: 'overdue',
        originalData: {}
    },
    {
        id: 2,
        dueDate: '2025-07-15',
        vesselID: 101,
        vessel: { id: 101, title: 'Test Vessel 2' },
        trainingTypeID: 201,
        trainingType: { id: 201, title: 'Fire Safety' },
        members: [{ id: 301, firstName: '<PERSON>', surname: '<PERSON>' }],
        status: { class: 'warning', label: 'Due Soon', isOverdue: false, dueWithinSevenDays: true },
        category: 'upcoming',
        originalData: {}
    },
    {
        id: 3,
        dueDate: '2025-07-01',
        vesselID: 100,
        vessel: { id: 100, title: 'Test Vessel 1' },
        trainingTypeID: 200,
        trainingType: { id: 200, title: 'Safety Training' },
        members: [{ id: 302, firstName: 'Bob', surname: 'Johnson' }],
        status: { class: 'success', label: 'Completed', isOverdue: false, dueWithinSevenDays: false },
        category: 'completed',
        originalData: { trainerID: 400, trainer: { id: 400, firstName: 'Trainer', surname: 'One' } }
    }
]

describe('useUnifiedTrainingFilters', () => {
    it('should return all data when no filters are applied', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        expect(result.current.filteredData).toHaveLength(3)
        expect(result.current.filteredData).toEqual(mockUnifiedData)
    })

    it('should filter by category correctly', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        act(() => {
            result.current.handleFilterChange({ type: 'category', data: 'overdue' })
        })

        expect(result.current.filteredData).toHaveLength(1)
        expect(result.current.filteredData[0].category).toBe('overdue')
    })

    it('should filter by vessel correctly', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        act(() => {
            result.current.handleFilterChange({ 
                type: 'vessel', 
                data: { value: 100 } 
            })
        })

        expect(result.current.filteredData).toHaveLength(2)
        expect(result.current.filteredData.every(item => item.vesselID === 100)).toBe(true)
    })

    it('should filter by training type correctly', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        act(() => {
            result.current.handleFilterChange({ 
                type: 'trainingType', 
                data: { value: 200 } 
            })
        })

        expect(result.current.filteredData).toHaveLength(2)
        expect(result.current.filteredData.every(item => item.trainingTypeID === 200)).toBe(true)
    })

    it('should filter by member correctly', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        act(() => {
            result.current.handleFilterChange({ 
                type: 'member', 
                data: { value: 300 } 
            })
        })

        expect(result.current.filteredData).toHaveLength(1)
        expect(result.current.filteredData[0].members.some(member => member.id === 300)).toBe(true)
    })

    it('should handle multiple filters correctly', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        // Apply vessel filter first
        act(() => {
            result.current.handleFilterChange({ 
                type: 'vessel', 
                data: { value: 100 } 
            })
        })

        // Then apply category filter
        act(() => {
            result.current.handleFilterChange({ 
                type: 'category', 
                data: 'completed' 
            })
        })

        expect(result.current.filteredData).toHaveLength(1)
        expect(result.current.filteredData[0].vesselID).toBe(100)
        expect(result.current.filteredData[0].category).toBe('completed')
    })

    it('should clear filters when data is null/undefined', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        act(() => {
            result.current.handleFilterChange({ 
                type: 'vessel', 
                data: null 
            })
        })

        expect(result.current.filteredData).toHaveLength(3)
    })

    it('should handle date range filtering', () => {
        const { result } = renderHook(() =>
            useUnifiedTrainingFilters({
                initialFilter: {},
                unifiedData: mockUnifiedData,
            })
        )

        act(() => {
            result.current.handleFilterChange({ 
                type: 'dateRange', 
                data: { 
                    startDate: new Date('2025-07-01'), 
                    endDate: new Date('2025-07-10') 
                } 
            })
        })

        expect(result.current.filteredData).toHaveLength(2)
    })
})
