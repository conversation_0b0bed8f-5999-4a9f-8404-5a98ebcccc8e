"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-isomorphic-layout-effec_c3f8d38d5459da0d6f1fcac2f51a6a1e";
exports.ids = ["vendor-chunks/use-isomorphic-layout-effec_c3f8d38d5459da0d6f1fcac2f51a6a1e"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/use-isomorphic-layout-effec_c3f8d38d5459da0d6f1fcac2f51a6a1e/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/use-isomorphic-layout-effec_c3f8d38d5459da0d6f1fcac2f51a6a1e/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : noop;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vdXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjX2MzZjhkMzhkNTQ1OWRhMGQ2ZjFmY2FjMmY1MWE2YTFlL25vZGVfbW9kdWxlcy91c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0L2Rpc3QvdXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjdC5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdDOztBQUV4Qzs7QUFFQTtBQUNBLHVCQUF1QixrREFBZTs7QUFFViIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vdXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjX2MzZjhkMzhkNTQ1OWRhMGQ2ZjFmY2FjMmY1MWE2YTFlL25vZGVfbW9kdWxlcy91c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0L2Rpc3QvdXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjdC5lc20uanM/YjMwYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbnZhciBpc0NsaWVudCA9IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbnZhciBub29wID0gZnVuY3Rpb24gbm9vcCgpIHt9O1xudmFyIGluZGV4ID0gaXNDbGllbnQgPyB1c2VMYXlvdXRFZmZlY3QgOiBub29wO1xuXG5leHBvcnQgeyBpbmRleCBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/use-isomorphic-layout-effec_c3f8d38d5459da0d6f1fcac2f51a6a1e/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\n");

/***/ })

};
;